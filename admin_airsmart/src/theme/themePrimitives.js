import { createTheme, alpha } from '@mui/material/styles';

const defaultTheme = createTheme();

const customShadows = [...defaultTheme.shadows];

// <PERSON><PERSON><PERSON><PERSON> sang tông màu đen trắng đơn giản
export const brand = {
  50: 'hsl(0, 0%, 98%)',
  100: 'hsl(0, 0%, 96%)',
  200: 'hsl(0, 0%, 90%)',
  300: 'hsl(0, 0%, 80%)',
  400: 'hsl(0, 0%, 65%)',
  500: 'hsl(0, 0%, 50%)',
  600: 'hsl(0, 0%, 35%)',
  700: 'hsl(0, 0%, 25%)',
  800: 'hsl(0, 0%, 15%)',
  900: 'hsl(0, 0%, 10%)',
};

export const gray = {
  50: 'hsl(0, 0%, 98%)',
  100: 'hsl(0, 0%, 96%)',
  200: 'hsl(0, 0%, 90%)',
  300: 'hsl(0, 0%, 80%)',
  400: 'hsl(0, 0%, 65%)',
  500: 'hsl(0, 0%, 50%)',
  600: 'hsl(0, 0%, 35%)',
  700: 'hsl(0, 0%, 25%)',
  800: 'hsl(0, 0%, 15%)',
  900: 'hsl(0, 0%, 10%)',
};

// Giữ lại các màu thông báo nhưng giảm độ bão hòa
export const green = {
  50: 'hsl(120, 20%, 98%)',
  100: 'hsl(120, 20%, 95%)',
  200: 'hsl(120, 15%, 90%)',
  300: 'hsl(120, 15%, 80%)',
  400: 'hsl(120, 10%, 65%)',
  500: 'hsl(120, 10%, 50%)',
  600: 'hsl(120, 5%, 35%)',
  700: 'hsl(120, 5%, 25%)',
  800: 'hsl(120, 5%, 15%)',
  900: 'hsl(120, 5%, 10%)',
};

export const orange = {
  50: 'hsl(30, 20%, 98%)',
  100: 'hsl(30, 20%, 95%)',
  200: 'hsl(30, 15%, 90%)',
  300: 'hsl(30, 15%, 80%)',
  400: 'hsl(30, 10%, 65%)',
  500: 'hsl(30, 10%, 50%)',
  600: 'hsl(30, 5%, 35%)',
  700: 'hsl(30, 5%, 25%)',
  800: 'hsl(30, 5%, 15%)',
  900: 'hsl(30, 5%, 10%)',
};

export const red = {
  50: 'hsl(0, 20%, 98%)',
  100: 'hsl(0, 20%, 95%)',
  200: 'hsl(0, 15%, 90%)',
  300: 'hsl(0, 15%, 80%)',
  400: 'hsl(0, 10%, 65%)',
  500: 'hsl(0, 10%, 50%)',
  600: 'hsl(0, 5%, 35%)',
  700: 'hsl(0, 5%, 25%)',
  800: 'hsl(0, 5%, 15%)',
  900: 'hsl(0, 5%, 10%)',
};

export const getDesignTokens = (mode) => {
  customShadows[1] =
    mode === 'dark'
      ? 'hsla(0, 0%, 0%, 0.7) 0px 4px 16px 0px, hsla(0, 0%, 0%, 0.8) 0px 8px 16px -5px'
      : 'hsla(0, 0%, 0%, 0.07) 0px 4px 16px 0px, hsla(0, 0%, 0%, 0.07) 0px 8px 16px -5px';

  return {
    palette: {
      mode,
      primary: {
        light: brand[200],
        main: brand[600],
        dark: brand[800],
        contrastText: brand[50],
        ...(mode === 'dark' && {
          contrastText: brand[50],
          light: brand[700],
          main: brand[500],
          dark: brand[300],
        }),
      },
      info: {
        light: brand[100],
        main: brand[500],
        dark: brand[700],
        contrastText: gray[50],
        ...(mode === 'dark' && {
          contrastText: brand[300],
          light: brand[500],
          main: brand[700],
          dark: brand[900],
        }),
      },
      warning: {
        light: orange[300],
        main: orange[500],
        dark: orange[700],
        ...(mode === 'dark' && {
          light: orange[400],
          main: orange[500],
          dark: orange[700],
        }),
      },
      error: {
        light: red[300],
        main: red[500],
        dark: red[700],
        ...(mode === 'dark' && {
          light: red[400],
          main: red[500],
          dark: red[700],
        }),
      },
      success: {
        light: green[300],
        main: green[500],
        dark: green[700],
        ...(mode === 'dark' && {
          light: green[400],
          main: green[500],
          dark: green[700],
        }),
      },
      grey: {
        ...gray,
      },
      divider: mode === 'dark' ? alpha(gray[700], 0.6) : alpha(gray[300], 0.4),
      background: {
        default: 'hsl(0, 0%, 98%)',
        paper: 'hsl(0, 0%, 100%)',
        ...(mode === 'dark' && { default: 'hsl(0, 0%, 10%)', paper: 'hsl(0, 0%, 15%)' }),
      },
      text: {
        primary: gray[800],
        secondary: gray[600],
        warning: orange[600],
        ...(mode === 'dark' && {
          primary: 'hsl(0, 0%, 100%)',
          secondary: gray[400],
        }),
      },
      action: {
        hover: alpha(gray[200], 0.2),
        selected: `${alpha(gray[200], 0.3)}`,
        ...(mode === 'dark' && {
          hover: alpha(gray[600], 0.2),
          selected: alpha(gray[600], 0.3),
        }),
      },
    },
    typography: {
      fontFamily: 'Inter, sans-serif',
      h1: {
        fontSize: defaultTheme.typography.pxToRem(48),
        fontWeight: 600,
        lineHeight: 1.2,
        letterSpacing: -0.5,
      },
      h2: {
        fontSize: defaultTheme.typography.pxToRem(36),
        fontWeight: 600,
        lineHeight: 1.2,
      },
      h3: {
        fontSize: defaultTheme.typography.pxToRem(30),
        lineHeight: 1.2,
      },
      h4: {
        fontSize: defaultTheme.typography.pxToRem(24),
        fontWeight: 600,
        lineHeight: 1.5,
      },
      h5: {
        fontSize: defaultTheme.typography.pxToRem(20),
        fontWeight: 600,
      },
      h6: {
        fontSize: defaultTheme.typography.pxToRem(18),
        fontWeight: 600,
      },
      subtitle1: {
        fontSize: defaultTheme.typography.pxToRem(18),
      },
      subtitle2: {
        fontSize: defaultTheme.typography.pxToRem(14),
        fontWeight: 500,
      },
      body1: {
        fontSize: defaultTheme.typography.pxToRem(14),
      },
      body2: {
        fontSize: defaultTheme.typography.pxToRem(14),
        fontWeight: 400,
      },
      caption: {
        fontSize: defaultTheme.typography.pxToRem(12),
        fontWeight: 400,
      },
    },
    shape: {
      borderRadius: 8,
    },
    shadows: customShadows,
  };
};

export const colorSchemes = {
  light: {
    palette: {
      primary: {
        light: brand[200],
        main: brand[600],
        dark: brand[800],
        contrastText: brand[50],
      },
      info: {
        light: brand[100],
        main: brand[500],
        dark: brand[700],
        contrastText: gray[50],
      },
      warning: {
        light: orange[300],
        main: orange[500],
        dark: orange[700],
      },
      error: {
        light: red[300],
        main: red[500],
        dark: red[700],
      },
      success: {
        light: green[300],
        main: green[500],
        dark: green[700],
      },
      grey: {
        ...gray,
      },
      divider: alpha(gray[300], 0.4),
      background: {
        default: 'hsl(0, 0%, 98%)',
        paper: 'hsl(0, 0%, 100%)',
      },
      text: {
        primary: gray[800],
        secondary: gray[600],
        warning: orange[600],
      },
      action: {
        hover: alpha(gray[200], 0.2),
        selected: `${alpha(gray[200], 0.3)}`,
      },
      baseShadow:
        'hsla(0, 0%, 0%, 0.07) 0px 4px 16px 0px, hsla(0, 0%, 0%, 0.07) 0px 8px 16px -5px',
    },
  },
  dark: {
    palette: {
      primary: {
        contrastText: brand[50],
        light: brand[300],
        main: brand[500],
        dark: brand[700],
      },
      info: {
        contrastText: brand[300],
        light: brand[500],
        main: brand[700],
        dark: brand[900],
      },
      warning: {
        light: orange[400],
        main: orange[500],
        dark: orange[700],
      },
      error: {
        light: red[400],
        main: red[500],
        dark: red[700],
      },
      success: {
        light: green[400],
        main: green[500],
        dark: green[700],
      },
      grey: {
        ...gray,
      },
      divider: alpha(gray[700], 0.6),
      background: {
        default: 'hsl(0, 0%, 10%)',
        paper: 'hsl(0, 0%, 15%)',
      },
      text: {
        primary: 'hsl(0, 0%, 100%)',
        secondary: gray[400],
      },
      action: {
        hover: alpha(gray[600], 0.2),
        selected: alpha(gray[600], 0.3),
      },
      baseShadow:
        'hsla(0, 0%, 0%, 0.7) 0px 4px 16px 0px, hsla(0, 0%, 0%, 0.8) 0px 8px 16px -5px',
    },
  },
};

export const typography = {
  fontFamily: 'Inter, sans-serif',
  h1: {
    fontSize: defaultTheme.typography.pxToRem(48),
    fontWeight: 600,
    lineHeight: 1.2,
    letterSpacing: -0.5,
  },
  h2: {
    fontSize: defaultTheme.typography.pxToRem(36),
    fontWeight: 600,
    lineHeight: 1.2,
  },
  h3: {
    fontSize: defaultTheme.typography.pxToRem(30),
    lineHeight: 1.2,
  },
  h4: {
    fontSize: defaultTheme.typography.pxToRem(24),
    fontWeight: 600,
    lineHeight: 1.5,
  },
  h5: {
    fontSize: defaultTheme.typography.pxToRem(20),
    fontWeight: 600,
  },
  h6: {
    fontSize: defaultTheme.typography.pxToRem(18),
    fontWeight: 600,
  },
  subtitle1: {
    fontSize: defaultTheme.typography.pxToRem(18),
  },
  subtitle2: {
    fontSize: defaultTheme.typography.pxToRem(14),
    fontWeight: 500,
  },
  body1: {
    fontSize: defaultTheme.typography.pxToRem(14),
  },
  body2: {
    fontSize: defaultTheme.typography.pxToRem(14),
    fontWeight: 400,
  },
  caption: {
    fontSize: defaultTheme.typography.pxToRem(12),
    fontWeight: 400,
  },
};

export const shape = {
  borderRadius: 8,
};

const defaultShadows = [
  'none',
  'var(--template-palette-baseShadow)',
  ...defaultTheme.shadows.slice(2),
];

export const shadows = defaultShadows;