import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { auth } from '../firebase';
import { onAuthStateChanged } from 'firebase/auth';

export default function RequireAuth({ children }) {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [hasAdminAccess, setHasAdminAccess] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      if (currentUser) {
        try {
          // Check if user has admin role
          const idTokenResult = await currentUser.getIdTokenResult();
          const role = idTokenResult.claims.role;
          
          // Allow admin and architect roles to access admin panel
          const allowedRoles = ['admin'];
          setHasAdminAccess(allowedRoles.includes(role));
          setUser(currentUser);
        } catch (error) {
          console.error('Error checking user role:', error);
          setHasAdminAccess(false);
          setUser(currentUser);
        }
      } else {
        setUser(null);
        setHasAdminAccess(false);
      }
      setLoading(false);
    });
    return () => unsubscribe();
  }, []);

  if (loading) {
    return null;
  }

  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (!hasAdminAccess) {
    return <Navigate to="/unauthorized" state={{ from: location }} replace />;
  }

  return children;
}