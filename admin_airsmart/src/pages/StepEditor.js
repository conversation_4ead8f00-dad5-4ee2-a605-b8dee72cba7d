import React, { useState, useEffect, useRef, Suspense } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  AppBar,
  Toolbar,
  IconButton,
  Tabs,
  Tab,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  FormControlLabel,
  CircularProgress,
  Snackbar,
  Alert,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
  Fab,
  Checkbox,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  ArrowBack,
  Save,
  Add as AddIcon,
  Delete as DeleteIcon,
  CameraAlt as CameraIcon,
  LocationOn as HotspotIcon,
  Layers as LayersIcon,
  ExpandMore as ExpandMoreIcon,
  ViewInAr as AttachModelIcon,
} from '@mui/icons-material';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Html, TransformControls } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';

import adminModuleService from '../services/adminModuleService';
import { convertToSignedUrl } from '../utils/wasabiHelper';
import AttachedModel3D, { TransformControlsWrapper } from '../components/AttachChildModel';
import * as THREE from 'three';

// Tab Panel Component for Step Dialog
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`step-tabpanel-${index}`}
      aria-labelledby={`step-tab-${index}`}
      style={{ padding: '16px 0' }}
      {...other}
    >
      {value === index && (
        <Box>{children}</Box>
      )}
    </div>
  );
}

// 3D Model Component - COMPLETELY FIXED recursive issue
function Model({ modelUrl, onModelClick, onMeshLayersExtracted, visibleLayers }) {
  const meshRef = useRef();
  const [loadedModel, setLoadedModel] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const meshObjectsRef = useRef(new Map());
  const callbackRef = useRef(onMeshLayersExtracted); // Store callback in ref
  
  // Update callback ref when prop changes
  useEffect(() => {
    callbackRef.current = onMeshLayersExtracted;
  }, [onMeshLayersExtracted]);
  
  useEffect(() => {
    if (modelUrl) {
      setLoading(true);
      setError(null);
      setLoadedModel(null);
      
      const gltfLoader = new GLTFLoader();
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
      gltfLoader.setDRACOLoader(dracoLoader);
      
      gltfLoader.load(
        modelUrl,
        (gltf) => {
          console.log('✅ GLTF model loaded successfully:', gltf);

          // Scale and center model
          const box = new THREE.Box3().setFromObject(gltf.scene);
          const size = box.getSize(new THREE.Vector3()).length();
          const scale = size > 0 ? 2 / size : 1;
          gltf.scene.scale.setScalar(scale);
          const center = box.getCenter(new THREE.Vector3());
          gltf.scene.position.copy(center).multiplyScalar(-scale);

          // Extract mesh layers
          const layers = [];
          const meshMap = new Map();
          
          gltf.scene.traverse((child) => {
            if (child.isMesh) {
              child.castShadow = true;
              child.receiveShadow = true;
              
              const layerName = child.name || `Mesh_${layers.length}`;
              const geometry = child.geometry;
              
              const layerInfo = {
                name: layerName,
                vertexCount: geometry.attributes.position ? geometry.attributes.position.count : 0,
                faceCount: geometry.index ? geometry.index.count / 3 : geometry.attributes.position.count / 3,
                material: child.material?.name || 'Default Material',
                uuid: child.uuid
              };
              
              layers.push(layerInfo);
              meshMap.set(layerName, child);
              
              console.log(`📊 Found mesh layer: ${layerName}`, layerInfo);
            }
          });

          // Store mesh objects in ref
          meshObjectsRef.current = meshMap;
          
          // FIXED: Use callback ref to avoid dependency issues
          if (callbackRef.current && layers.length > 0) {
            callbackRef.current(layers, meshMap);
          }

          setLoadedModel(gltf);
          setLoading(false);
        },
        (progress) => {
          if (progress.lengthComputable) {
            const percentComplete = (progress.loaded / progress.total) * 100;
            console.log('📊 GLTF loading progress:', percentComplete.toFixed(2) + '%');
          }
        },
        (error) => {
          console.error('❌ Error loading GLTF model:', error);
          setError(error);
          setLoading(false);
        }
      );
    } else {
      setLoadedModel(null);
      setLoading(false);
      setError(null);
    }
  }, [modelUrl]); // FIXED: Clean dependency array

  // FIXED: Direct effect for visibility updates
  useEffect(() => {
    if (meshObjectsRef.current.size > 0 && visibleLayers) {
      meshObjectsRef.current.forEach((meshObject, layerName) => {
        const isVisible = visibleLayers.has(layerName);
        
        // Only update if visibility actually changed
        if (meshObject.visible !== isVisible) {
          meshObject.visible = isVisible;
          console.log(`🔧 Layer ${layerName} visibility: ${isVisible}`);
        }
      });
    }
  }, [visibleLayers]); // FIXED: Direct dependency, no callback

  // Show loading state
  if (loading) {
    return (
      <Html center>
        <div style={{
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '12px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          textAlign: 'center'
        }}>
          Loading GLTF Model...
        </div>
      </Html>
    );
  }

  // Show error state
  if (error) {
    return (
      <Html center>
        <div style={{
          background: 'rgba(255,0,0,0.8)',
          color: 'white',
          padding: '12px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          textAlign: 'center',
          maxWidth: '200px'
        }}>
          <div>❌ Failed to load GLTF model</div>
          <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}>
            Check URL and CORS settings
          </div>
        </div>
      </Html>
    );
  }

  // Only render if we have a valid model
  if (!loadedModel) {
    return null;
  }

  return (
    <primitive 
      ref={meshRef}
      object={loadedModel.scene} 
      scale={[1, 1, 1]}
      onClick={onModelClick}
    />
  );
}

// Fallback component for when no model is loaded
function DefaultScene() {
  return (
    <mesh>
      <boxGeometry args={[2, 2, 2]} />
      <meshStandardMaterial color="#cccccc" />
    </mesh>
  );
}

// 3D Model Viewer Component
function ModelViewer({
  modelUrl,
  hotspots = [],
  onAddHotspot,
  onUpdateCamera,
  cameraPosition,
  selectedHotspot,
  onSelectHotspot,
  showHotspots = true,
  onMeshLayersExtracted, // New prop for layer extraction callback
  visibleLayers, // New prop for controlling layer visibility
  attachedModels = [],
  selectedAttachedModel,
  onSelectAttachedModel,
  onAttachedModelTransform,
  showAttachedModels = false,
  transformMode = 'translate',
  onTransformStart,
  onTransformEnd,
  isTransforming = false,
  attachedModelRefs
}) {
  const controlsRef = useRef();

  // Effect to sync camera position when cameraPosition prop changes
  useEffect(() => {
    if (controlsRef.current && cameraPosition) {
      const controls = controlsRef.current;
      const camera = controls.object;
      
      // Set camera position
      if (cameraPosition.position) {
        camera.position.set(
          cameraPosition.position[0],
          cameraPosition.position[1],
          cameraPosition.position[2]
        );
      }
      
      // Set camera target (look at)
      if (cameraPosition.target) {
        controls.target.set(
          cameraPosition.target[0],
          cameraPosition.target[1],
          cameraPosition.target[2 ]
        );
      }
      
      // Update the controls to apply changes
      controls.update();
    }
  }, [cameraPosition]);

  const handleModelClick = (event) => {
    if (!onAddHotspot) return;
    
    event.stopPropagation();
    
    // Convert screen coordinates to 3D world coordinates
    const intersects = event.intersections;
    if (intersects.length > 0) {
      const position = [
        intersects[0].point.x,
        intersects[0].point.y,
        intersects[0].point.z
      ];
      onAddHotspot(position);
    }
  };

  const handleCameraChange = () => {
    if (controlsRef.current && onUpdateCamera) {
      const camera = controlsRef.current.object;
      onUpdateCamera({
        position: [camera.position.x, camera.position.y, camera.position.z],
        target: [
          controlsRef.current.target.x,
          controlsRef.current.target.y,
          controlsRef.current.target.z
        ]
      });
    }
  };

  return (
    <>
      <OrbitControls
        ref={controlsRef}
        onChange={handleCameraChange}
        enablePan={!isTransforming}
        enableZoom={!isTransforming}
        enableRotate={!isTransforming}
        enableDamping={true}
        dampingFactor={0.05}
        rotateSpeed={1.2}
        zoomSpeed={1.2}
        panSpeed={1.0}
        autoRotate={false}
        // Cải thiện responsive và smooth movement
        minDistance={0.5}
        maxDistance={50}
        minPolarAngle={0}
        maxPolarAngle={Math.PI}
        // Cải thiện performance
        enableKeys={true}
        keyPanSpeed={7.0}
        screenSpacePanning={false}
        // Touch controls cho mobile
        touches={{
          ONE: THREE.TOUCH.ROTATE,
          TWO: THREE.TOUCH.DOLLY_PAN
        }}
        // Mouse controls optimization
        mouseButtons={{
          LEFT: THREE.MOUSE.ROTATE,
          MIDDLE: THREE.MOUSE.DOLLY,
          RIGHT: THREE.MOUSE.PAN
        }}
        // Prevent camera from going through objects
        target0={undefined}
        position0={undefined}
        zoom0={undefined}
      />
      
      {/* 3D Model or Default Scene with layer support */}
      <Suspense fallback={null}>
        {modelUrl ? (
          <Model 
            modelUrl={modelUrl} 
            onModelClick={handleModelClick}
            onMeshLayersExtracted={onMeshLayersExtracted} // Use prop from parent
            visibleLayers={visibleLayers}
          />
        ) : (
          <DefaultScene />
        )}
      </Suspense>
      
      {/* Hotspots - chỉ hiển thị khi showHotspots = true */}
      {showHotspots && hotspots.map((hotspot, index) => (
        <mesh 
          key={hotspot.id || index}
          position={hotspot.position}
          onClick={(e) => {
            e.stopPropagation();
            onSelectHotspot?.(hotspot);
          }}
        >
          <sphereGeometry args={[0.1, 16, 16]} />
          <meshBasicMaterial 
            color={selectedHotspot?.id === hotspot.id ? '#ff4444' : '#44ff44'} 
          />
          <Html distanceFactor={10}>
            <div 
              style={{
                background: 'rgba(0,0,0,0.8)',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '4px',
                fontSize: '12px',
                pointerEvents: 'none'
              }}
            >
              {hotspot.label}
            </div>
          </Html>
        </mesh>
      ))}

      {/* Attached Models */}
      {showAttachedModels && attachedModels.map((attachedModel) => {
        // Create ref for this model if it doesn't exist
        if (!attachedModelRefs.current[attachedModel.id]) {
          attachedModelRefs.current[attachedModel.id] = React.createRef();
        }

        return (
          <AttachedModel3D
            key={attachedModel.id}
            ref={attachedModelRefs.current[attachedModel.id]}
            attachedModel={attachedModel}
            onSelect={onSelectAttachedModel}
          />
        );
      })}

      {/* Transform Controls for Selected Model */}
      {showAttachedModels && selectedAttachedModel && attachedModelRefs.current[selectedAttachedModel.id] && (
        <TransformControlsWrapper
          targetRef={attachedModelRefs.current[selectedAttachedModel.id]}
          mode={transformMode}
          onChange={onAttachedModelTransform}
          onMouseDown={onTransformStart}
          onMouseUp={onTransformEnd}
          enabled={true}
        />
      )}

      {/* Default lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={1.2} castShadow />
      <directionalLight position={[-10, -10, -5]} intensity={0.8} />
      <directionalLight position={[0, 10, 10]} intensity={0.8} />
      <pointLight position={[0, 10, 0]} intensity={0.7} />
      <pointLight position={[5, 0, 5]} intensity={0.5} />
      <pointLight position={[-5, 0, -5]} intensity={0.5} />
    </>
  );
}

// Loading component for 3D canvas
function CanvasLoader() {
  return (
    <Box sx={{ 
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 2
    }}>
      <CircularProgress />
      <Typography variant="body2" color="text.secondary">
        Loading 3D Model...
      </Typography>
    </Box>
  );
}

// Attach Model Manager Component
function AttachModelManager({
  attachedModels,
  setAttachedModels,
  selectedAttachedModel,
  setSelectedAttachedModel,
  setSnackbar,
  transformMode,
  setTransformMode,
  attachedModelRefs,
  moduleId
}) {
  const fileInputRef = useRef();

  const handleImportGLB = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.glb')) {
      setSnackbar({
        open: true,
        message: 'Please select a GLB file',
        severity: 'error'
      });
      return;
    }

    try {
      // Create a temporary URL for the file
      const tempUrl = URL.createObjectURL(file);

      // Create new attached model
      const newAttachedModel = {
        id: `attached_${Date.now()}`,
        fileName: file.name,
        modelUrl: tempUrl,
        position: [0, 0, 0],
        rotation: [0, 0, 0],
        scale: [1, 1, 1]
      };

      setAttachedModels(prev => [...prev, newAttachedModel]);
      setSelectedAttachedModel(newAttachedModel);

      setSnackbar({
        open: true,
        message: `GLB model "${file.name}" imported successfully!`,
        severity: 'success'
      });
    } catch (error) {
      console.error('Error importing GLB file:', error);
      setSnackbar({
        open: true,
        message: 'Error importing GLB file',
        severity: 'error'
      });
    }

    // Reset file input
    event.target.value = '';
  };

  const handleDeleteModel = (modelId) => {
    setAttachedModels(prev => prev.filter(model => model.id !== modelId));
    if (selectedAttachedModel?.id === modelId) {
      setSelectedAttachedModel(null);
    }
    setSnackbar({
      open: true,
      message: 'Attached model deleted',
      severity: 'info'
    });
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">
          Attached Models ({attachedModels.length})
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleImportGLB}
        >
          Import GLB
        </Button>
      </Box>

      <Alert severity="info" sx={{ mb: 2 }}>
        Import GLB models and attach them to the main 3D model. You can position and rotate them as needed.
      </Alert>

      {/* Transform Mode Controls */}
      <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
        <Button
          variant={transformMode === 'translate' ? 'contained' : 'outlined'}
          size="small"
          onClick={() => setTransformMode('translate')}
        >
          Move
        </Button>
        <Button
          variant={transformMode === 'rotate' ? 'contained' : 'outlined'}
          size="small"
          onClick={() => setTransformMode('rotate')}
        >
          Rotate
        </Button>
        <Button
          variant={transformMode === 'scale' ? 'contained' : 'outlined'}
          size="small"
          onClick={() => setTransformMode('scale')}
        >
          Scale
        </Button>
      </Box>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".glb"
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />

      {/* Attached Models List */}
      {attachedModels.length > 0 ? (
        <List>
          {attachedModels.map((model) => (
            <ListItem
              key={model.id}
              button
              selected={selectedAttachedModel?.id === model.id}
              onClick={() => setSelectedAttachedModel(model)}
            >
              <ListItemText
                primary={model.fileName}
                secondary={`Position: [${model.position.map(p => Number(p).toFixed(1)).join(', ')}]`}
              />
              <ListItemSecondaryAction>
                <IconButton
                  edge="end"
                  onClick={() => handleDeleteModel(model.id)}
                >
                  <DeleteIcon />
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
      ) : (
        <Box sx={{ textAlign: 'center', p: 3, bgcolor: 'background.paper' }}>
          <Typography color="text.secondary">
            No attached models yet. Click "Import GLB" to add one.
          </Typography>
        </Box>
      )}

      {/* Selected Model Details */}
      {selectedAttachedModel && (
        <Card variant="outlined" sx={{ mt: 2 }}>
          <CardContent>
            <Typography variant="subtitle1" gutterBottom>
              Edit Selected Model
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
              <TextField
                label="File Name"
                value={selectedAttachedModel.fileName}
                fullWidth
                size="small"
                disabled
              />

              <Typography variant="subtitle2">Position (x, y, z)</Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="X"
                  type="number"
                  inputProps={{ step: 0.1 }}
                  value={Number(selectedAttachedModel.position[0]).toFixed(2)}
                  onChange={(e) => {
                    const newPosition = [...selectedAttachedModel.position];
                    newPosition[0] = parseFloat(e.target.value) || 0;

                    const updatedModels = attachedModels.map(m =>
                      m.id === selectedAttachedModel.id
                        ? { ...m, position: newPosition }
                        : m
                    );
                    setAttachedModels(updatedModels);
                    setSelectedAttachedModel({ ...selectedAttachedModel, position: newPosition });

                    // Update the 3D object directly
                    if (attachedModelRefs.current[selectedAttachedModel.id]?.current) {
                      attachedModelRefs.current[selectedAttachedModel.id].current.position.set(...newPosition);
                    }
                  }}
                  fullWidth
                  size="small"
                />
                <TextField
                  label="Y"
                  type="number"
                  inputProps={{ step: 0.1 }}
                  value={Number(selectedAttachedModel.position[1]).toFixed(2)}
                  onChange={(e) => {
                    const newPosition = [...selectedAttachedModel.position];
                    newPosition[1] = parseFloat(e.target.value) || 0;

                    const updatedModels = attachedModels.map(m =>
                      m.id === selectedAttachedModel.id
                        ? { ...m, position: newPosition }
                        : m
                    );
                    setAttachedModels(updatedModels);
                    setSelectedAttachedModel({ ...selectedAttachedModel, position: newPosition });

                    // Update the 3D object directly
                    if (attachedModelRefs.current[selectedAttachedModel.id]?.current) {
                      attachedModelRefs.current[selectedAttachedModel.id].current.position.set(...newPosition);
                    }
                  }}
                  fullWidth
                  size="small"
                />
                <TextField
                  label="Z"
                  type="number"
                  inputProps={{ step: 0.1 }}
                  value={Number(selectedAttachedModel.position[2]).toFixed(2)}
                  onChange={(e) => {
                    const newPosition = [...selectedAttachedModel.position];
                    newPosition[2] = parseFloat(e.target.value) || 0;

                    const updatedModels = attachedModels.map(m =>
                      m.id === selectedAttachedModel.id
                        ? { ...m, position: newPosition }
                        : m
                    );
                    setAttachedModels(updatedModels);
                    setSelectedAttachedModel({ ...selectedAttachedModel, position: newPosition });

                    // Update the 3D object directly
                    if (attachedModelRefs.current[selectedAttachedModel.id]?.current) {
                      attachedModelRefs.current[selectedAttachedModel.id].current.position.set(...newPosition);
                    }
                  }}
                  fullWidth
                  size="small"
                />
              </Box>

              <Typography variant="subtitle2" sx={{ mt: 2 }}>Rotation (x, y, z) - Radians</Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="X"
                  type="number"
                  inputProps={{ step: 0.1 }}
                  value={Number(selectedAttachedModel.rotation?.[0] || 0).toFixed(2)}
                  onChange={(e) => {
                    const newRotation = [...(selectedAttachedModel.rotation || [0, 0, 0])];
                    newRotation[0] = parseFloat(e.target.value) || 0;

                    const updatedModels = attachedModels.map(m =>
                      m.id === selectedAttachedModel.id
                        ? { ...m, rotation: newRotation }
                        : m
                    );
                    setAttachedModels(updatedModels);
                    setSelectedAttachedModel({ ...selectedAttachedModel, rotation: newRotation });

                    // Update the 3D object directly
                    if (attachedModelRefs.current[selectedAttachedModel.id]?.current) {
                      attachedModelRefs.current[selectedAttachedModel.id].current.rotation.set(...newRotation);
                    }
                  }}
                  fullWidth
                  size="small"
                />
                <TextField
                  label="Y"
                  type="number"
                  inputProps={{ step: 0.1 }}
                  value={Number(selectedAttachedModel.rotation?.[1] || 0).toFixed(2)}
                  onChange={(e) => {
                    const newRotation = [...(selectedAttachedModel.rotation || [0, 0, 0])];
                    newRotation[1] = parseFloat(e.target.value) || 0;

                    const updatedModels = attachedModels.map(m =>
                      m.id === selectedAttachedModel.id
                        ? { ...m, rotation: newRotation }
                        : m
                    );
                    setAttachedModels(updatedModels);
                    setSelectedAttachedModel({ ...selectedAttachedModel, rotation: newRotation });

                    // Update the 3D object directly
                    if (attachedModelRefs.current[selectedAttachedModel.id]?.current) {
                      attachedModelRefs.current[selectedAttachedModel.id].current.rotation.set(...newRotation);
                    }
                  }}
                  fullWidth
                  size="small"
                />
                <TextField
                  label="Z"
                  type="number"
                  inputProps={{ step: 0.1 }}
                  value={Number(selectedAttachedModel.rotation?.[2] || 0).toFixed(2)}
                  onChange={(e) => {
                    const newRotation = [...(selectedAttachedModel.rotation || [0, 0, 0])];
                    newRotation[2] = parseFloat(e.target.value) || 0;

                    const updatedModels = attachedModels.map(m =>
                      m.id === selectedAttachedModel.id
                        ? { ...m, rotation: newRotation }
                        : m
                    );
                    setAttachedModels(updatedModels);
                    setSelectedAttachedModel({ ...selectedAttachedModel, rotation: newRotation });

                    // Update the 3D object directly
                    if (attachedModelRefs.current[selectedAttachedModel.id]?.current) {
                      attachedModelRefs.current[selectedAttachedModel.id].current.rotation.set(...newRotation);
                    }
                  }}
                  fullWidth
                  size="small"
                />
              </Box>

              <Typography variant="subtitle2" sx={{ mt: 2 }}>Scale (x, y, z)</Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="X"
                  type="number"
                  inputProps={{ step: 0.1 }}
                  value={Number(selectedAttachedModel.scale?.[0] || 1).toFixed(2)}
                  onChange={(e) => {
                    const newScale = [...(selectedAttachedModel.scale || [1, 1, 1])];
                    newScale[0] = parseFloat(e.target.value) || 1;

                    const updatedModels = attachedModels.map(m =>
                      m.id === selectedAttachedModel.id
                        ? { ...m, scale: newScale }
                        : m
                    );
                    setAttachedModels(updatedModels);
                    setSelectedAttachedModel({ ...selectedAttachedModel, scale: newScale });

                    // Update the 3D object directly
                    if (attachedModelRefs.current[selectedAttachedModel.id]?.current) {
                      attachedModelRefs.current[selectedAttachedModel.id].current.scale.set(...newScale);
                    }
                  }}
                  fullWidth
                  size="small"
                />
                <TextField
                  label="Y"
                  type="number"
                  inputProps={{ step: 0.1 }}
                  value={Number(selectedAttachedModel.scale?.[1] || 1).toFixed(2)}
                  onChange={(e) => {
                    const newScale = [...(selectedAttachedModel.scale || [1, 1, 1])];
                    newScale[1] = parseFloat(e.target.value) || 1;

                    const updatedModels = attachedModels.map(m =>
                      m.id === selectedAttachedModel.id
                        ? { ...m, scale: newScale }
                        : m
                    );
                    setAttachedModels(updatedModels);
                    setSelectedAttachedModel({ ...selectedAttachedModel, scale: newScale });

                    // Update the 3D object directly
                    if (attachedModelRefs.current[selectedAttachedModel.id]?.current) {
                      attachedModelRefs.current[selectedAttachedModel.id].current.scale.set(...newScale);
                    }
                  }}
                  fullWidth
                  size="small"
                />
                <TextField
                  label="Z"
                  type="number"
                  inputProps={{ step: 0.1 }}
                  value={Number(selectedAttachedModel.scale?.[2] || 1).toFixed(2)}
                  onChange={(e) => {
                    const newScale = [...(selectedAttachedModel.scale || [1, 1, 1])];
                    newScale[2] = parseFloat(e.target.value) || 1;

                    const updatedModels = attachedModels.map(m =>
                      m.id === selectedAttachedModel.id
                        ? { ...m, scale: newScale }
                        : m
                    );
                    setAttachedModels(updatedModels);
                    setSelectedAttachedModel({ ...selectedAttachedModel, scale: newScale });

                    // Update the 3D object directly
                    if (attachedModelRefs.current[selectedAttachedModel.id]?.current) {
                      attachedModelRefs.current[selectedAttachedModel.id].current.scale.set(...newScale);
                    }
                  }}
                  fullWidth
                  size="small"
                />
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}

function StepEditor() {
  const location = useLocation();
  const navigate = useNavigate();
  const { moduleId, moduleData } = location.state || {};
  
  const [loading, setLoading] = useState(false);
  const [modelUrl, setModelUrl] = useState('');
  const [tabIndex, setTabIndex] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Step data state - initialized for new step
  const [stepData, setStepData] = useState({
    title: '',
    content: '',
    // Remove order initialization - let backend auto-calculate
    autoPlay: false,
    tags: []
  });

  // 3D visualization state - initialized for new step
  const [cameraPosition, setCameraPosition] = useState({
    position: [0, 2, 5],
    target: [0, 0, 0]
  });
  const [hotspots, setHotspots] = useState([]);
  const [selectedHotspot, setSelectedHotspot] = useState(null);
  const [meshLayers, setMeshLayers] = useState([]); // State for mesh layers
  const [visibleLayers, setVisibleLayers] = useState(new Set()); // Track visible layers

  // Attached models state
  const [attachedModels, setAttachedModels] = useState([]);
  const [selectedAttachedModel, setSelectedAttachedModel] = useState(null);
  const [transformMode, setTransformMode] = useState('translate'); // 'translate', 'rotate', or 'scale'
  const [isTransforming, setIsTransforming] = useState(false);
  const attachedModelRefs = useRef({});
  const transformTimeoutRef = useRef(null);

  // Sync 3D object position/rotation when selectedAttachedModel changes
  useEffect(() => {
    if (selectedAttachedModel && attachedModelRefs.current[selectedAttachedModel.id]) {
      const ref = attachedModelRefs.current[selectedAttachedModel.id];
      if (ref.current) {
        // Update 3D object to match selected model's stored position/rotation/scale
        if (selectedAttachedModel.position) {
          ref.current.position.set(...selectedAttachedModel.position);
        }
        if (selectedAttachedModel.rotation) {
          ref.current.rotation.set(...selectedAttachedModel.rotation);
        }
        if (selectedAttachedModel.scale) {
          ref.current.scale.set(...selectedAttachedModel.scale);
        }
      }
    }
  }, [selectedAttachedModel]);

  // Default camera position constant
  const DEFAULT_CAMERA_POSITION = {
    position: [0, 2, 5],
    target: [0, 0, 0]
  };

  // FIXED: Memoize callback to prevent recreation on every render
  const handleMeshLayersExtracted = React.useCallback((layers, meshMap) => {
    console.log('📊 Mesh layers extracted:', layers);
    setMeshLayers(layers);
    
    // Initially, all layers are visible
    const initialVisibleLayers = new Set(layers.map(layer => layer.name));
    setVisibleLayers(initialVisibleLayers);
    
    setSnackbar({
      open: true,
      message: `Found ${layers.length} mesh layers in model`,
      severity: 'success'
    });
  }, []); // FIXED: Empty dependency - callback won't change

  // Load module data on component mount
  useEffect(() => {
    if (!moduleId) {
      // Navigate back if no moduleId provided
      navigate('/modules');
      return;
    }
    
    // Load module data to get model URL
    const loadModuleData = async () => {
      setLoading(true);
      try {
        let currentModuleData = moduleData;
        if (!currentModuleData) {
          currentModuleData = await adminModuleService.getModule(moduleId);
        }
        
        // Set model URL from module data
        if (currentModuleData && currentModuleData.modelUrl) {
          const signedUrl = convertToSignedUrl(currentModuleData.modelUrl);
          setModelUrl(signedUrl);
        }
      } catch (error) {
        console.error('Error loading module data:', error);
        setSnackbar({
          open: true,
          message: `Error loading module data: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };
    
    loadModuleData();
  }, [moduleId, moduleData, navigate]);

  // Effect to update mesh layers when model URL changes
  useEffect(() => {
    if (modelUrl) {
      // Reset layers when model URL is set
      setMeshLayers([]);
      setVisibleLayers(new Set());
    }
  }, [modelUrl]);

  const handleSaveStep = async () => {
    // Validate required fields
    if (!stepData.title.trim()) {
      setSnackbar({
        open: true,
        message: 'Topic title is required',
        severity: 'error'
      });
      return;
    }

    if (!stepData.content.trim()) {
      setSnackbar({
        open: true,
        message: 'Topic content is required',
        severity: 'error'
      });
      return;
    }

    setLoading(true);
    try {
      // Check if there are attached models to upload
      const hasAttachedModels = attachedModels && attachedModels.length > 0;
      const hasBlobModels = hasAttachedModels && attachedModels.some(model => model.modelUrl.startsWith('blob:'));

      let finalAttachedModels = attachedModels;

      if (hasBlobModels) {
        setSnackbar({
          open: true,
          message: 'Uploading attached models to cloud storage...',
          severity: 'info'
        });

        // Upload attached models first and get Wasabi URLs
        finalAttachedModels = await adminModuleService.uploadAttachedModels(attachedModels, moduleId);
        console.log('🔄 Uploaded models with Wasabi URLs:', finalAttachedModels);
      }

      // Create the complete step object with final attached models (Wasabi URLs)
      const finalStepData = {
        ...stepData,
        // Don't include order - let backend auto-calculate
        camera: cameraPosition,
        hotspots: hotspots,
        attachedModels: finalAttachedModels // ← Use Wasabi URLs here
      };

      console.log('🔍 Final step data before saving to Firestore:', {
        attachedModelsCount: finalAttachedModels?.length || 0,
        attachedModels: finalAttachedModels?.map(model => ({
          id: model.id,
          fileName: model.fileName,
          modelUrl: model.modelUrl,
          wasabiKey: model.wasabiKey
        }))
      });

      // Create new step with uploaded model URLs (finalStepData already has Wasabi URLs)
      await adminModuleService.createStep(moduleId, finalStepData);
      setSnackbar({
        open: true,
        message: hasAttachedModels
          ? 'Topic created successfully with attached models!'
          : 'Topic created successfully!',
        severity: 'success'
      });

      // Navigate back after a brief delay to show the success message
      setTimeout(() => {
        navigate('/modules');
      }, 1500);
    } catch (error) {
      console.error('Error creating topic:', error);
      setSnackbar({
        open: true,
        message: `Error creating topic: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddHotspot = (position) => {
    // Chỉ cho phép thêm hotspot khi đang ở tab Hotspots (index 1)
    if (tabIndex !== 1) {
      setSnackbar({
        open: true,
        message: 'Please switch to the "Hotspots" tab to add hotspots',
        severity: 'warning'
      });
      return;
    }

    const newHotspot = {
      id: `hotspot_${Date.now()}`,
      position,
      label: `Hotspot ${hotspots.length + 1}`,
      action: 'info',
      description: ''
    };
    setHotspots([...hotspots, newHotspot]);
    setSelectedHotspot(newHotspot);
  };

  const handleUpdateCamera = (newCamera) => {
    setCameraPosition(newCamera);
  };

  const handleResetCamera = () => {
    setCameraPosition(DEFAULT_CAMERA_POSITION);
    setSnackbar({
      open: true,
      message: 'Camera position reset to default',
      severity: 'info'
    });
  };

  // Attached models handlers
  const handleSelectAttachedModel = (model) => {
    // Clear any pending transform updates
    if (transformTimeoutRef.current) {
      clearTimeout(transformTimeoutRef.current);
    }
    // Reset transform state when switching models
    setIsTransforming(false);
    setSelectedAttachedModel(model);
  };

  const handleAttachedModelTransform = () => {
    // Prevent updates during model switching
    if (!selectedAttachedModel || isTransforming === false) {
      return;
    }

    // Clear previous timeout
    if (transformTimeoutRef.current) {
      clearTimeout(transformTimeoutRef.current);
    }

    // Debounce the update to prevent excessive re-renders
    transformTimeoutRef.current = setTimeout(() => {
      if (attachedModelRefs.current[selectedAttachedModel.id]) {
        const ref = attachedModelRefs.current[selectedAttachedModel.id];
        if (ref.current) {
          const position = ref.current.position.toArray();
          const rotation = ref.current.rotation.toArray().slice(0, 3);
          const scale = ref.current.scale.toArray();

          // Update both state and selected model for real-time UI update
          const updatedModel = { ...selectedAttachedModel, position, rotation, scale };
          setSelectedAttachedModel(updatedModel);

          setAttachedModels(prev =>
            prev.map(model =>
              model.id === selectedAttachedModel.id
                ? updatedModel
                : model
            )
          );
        }
      }
    }, 16); // ~60fps update rate
  };

  const handleTransformStart = () => {
    setIsTransforming(true);
  };

  const handleTransformEnd = () => {
    setIsTransforming(false);
  };

  // OPTIMIZED: Memoize layer visibility change handler to prevent re-renders
  const handleLayerVisibilityChange = React.useCallback((layerName, isVisible) => {
    setVisibleLayers(prevLayers => {
      const newVisibleLayers = new Set(prevLayers);
      if (isVisible) {
        newVisibleLayers.add(layerName);
      } else {
        newVisibleLayers.delete(layerName);
      }
      return newVisibleLayers;
    });
  }, []); // Empty dependency array since we use functional update

  // OPTIMIZED: Memoized LayerItem component to prevent unnecessary re-renders
  const LayerItem = React.memo(({ layer, index, isVisible, onVisibilityChange }) => {
    return (
      <Accordion key={index} elevation={0} sx={{ bgcolor: 'transparent' }}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls={`panel${index}-content`}
          id={`panel${index}-header`}
          sx={{ 
            p: 1,
            borderRadius: 1,
            bgcolor: isVisible ? 'rgba(0,255,0,0.1)' : 'rgba(255,0,0,0.1)',
            transition: 'background-color 0.3s',
            '&:hover': {
              bgcolor: isVisible ? 'rgba(0,255,0,0.2)' : 'rgba(255,0,0,0.2)',
            }
          }}
        >
          <Checkbox
            checked={isVisible}
            onChange={(e) => onVisibilityChange(layer.name, e.target.checked)}
            size="small"
            sx={{ 
              color: isVisible ? 'green' : 'red',
              '&.Mui-checked': {
                color: 'green',
              },
              mr: 1
            }}
          />
          <Typography variant="subtitle2" sx={{ flex: 1 }}>
            {layer.name}
          </Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ bgcolor: 'background.paper' }}>
          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
            {`Vertices: ${layer.vertexCount}, Faces: ${layer.faceCount}`}
          </Typography>
        </AccordionDetails>
      </Accordion>
    );
  });

  LayerItem.displayName = 'LayerItem';

  return (
    <Box sx={{ height: 'calc(100vh - 88px)', overflow: 'hidden' }}>
      {/* Header/Toolbar */}
      <Paper sx={{ mb: 2 }} elevation={2}>
        <Toolbar>
          <IconButton edge="start" color="inherit" onClick={() => navigate('/modules')}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" sx={{ ml: 2, flex: 1 }}>
            Create New Topic
          </Typography>
          <Button 
            variant="contained"
            color="success"
            startIcon={<Save />}
            onClick={handleSaveStep}
            disabled={loading || !stepData.title || !stepData.content}
          >
            Save Topic
          </Button>
        </Toolbar>
      </Paper>

      {/* Main Content - Remove Grid container padding */}
      <Box sx={{ 
        display: 'flex', 
        height: 'calc(100% - 80px)', 
        gap: 1, // Smaller gap
        px: 2 // Add padding to container instead
      }}>
        {/* 3D Preview Panel - Left Side */}
        <Box sx={{ 
          flex: '1 1 75%', // Take 75% of available space
          height: '100%',
          minWidth: 0 // Important for flex shrinking
        }}>
          <Paper sx={{ height: '100%', position: 'relative', overflow: 'hidden' }} elevation={3}>
            {/* Loading state */}
            {loading && !modelUrl && <CanvasLoader />}
            
            {/* 3D Canvas */}
            {modelUrl && (
              <Canvas style={{ height: '100%', width: '100%' }}>
                <ModelViewer
                  modelUrl={modelUrl}
                  hotspots={hotspots}
                  onAddHotspot={handleAddHotspot}
                  onUpdateCamera={handleUpdateCamera}
                  cameraPosition={cameraPosition}
                  selectedHotspot={selectedHotspot}
                  onSelectHotspot={setSelectedHotspot}
                  showHotspots={tabIndex === 1} // Chỉ hiển thị hotspots khi ở tab Hotspots
                  onMeshLayersExtracted={handleMeshLayersExtracted} // Sử dụng callback đã định nghĩa
                  visibleLayers={visibleLayers} // Truyền prop điều khiển hiển thị layer
                  attachedModels={attachedModels}
                  selectedAttachedModel={selectedAttachedModel}
                  onSelectAttachedModel={handleSelectAttachedModel}
                  onAttachedModelTransform={handleAttachedModelTransform}
                  showAttachedModels={tabIndex === 3}
                  transformMode={transformMode}
                  onTransformStart={handleTransformStart}
                  onTransformEnd={handleTransformEnd}
                  isTransforming={isTransforming}
                  attachedModelRefs={attachedModelRefs}
                />
              </Canvas>
            )}
            
            {/* 3D Controls Toolbar */}
            <Box sx={{ 
              position: 'absolute', 
              top: 16, 
              left: 16,
              display: 'flex',
              flexDirection: 'column',
              gap: 2
            }}>
              <Tooltip title="Capture Camera Position">
                <Fab size="small" color="primary" onClick={() => {
                  setSnackbar({
                    open: true,
                    message: 'Camera position captured',
                    severity: 'info'
                  });
                }}>
                  <CameraIcon />
                </Fab>
              </Tooltip>
              <Tooltip title="Add Hotspot (Click on model)">
                <Fab size="small" color="secondary">
                  <HotspotIcon />
                </Fab>
              </Tooltip>
              <Tooltip title="Reset Camera to Default">
                <Fab size="small" color="default" onClick={handleResetCamera}>
                  <ArrowBack />
                </Fab>
              </Tooltip>
            </Box>
            
            {/* Camera Position Info */}
            <Box sx={{ 
              position: 'absolute', 
              bottom: 16, 
              left: 16, 
              right: 16,
              background: 'rgba(0,0,0,0.5)',
              borderRadius: 1,
              p: 1,
              color: 'white',
              fontSize: '12px'
            }}>
              <Typography variant="caption" sx={{ color: 'white', display: 'block' }}>
                Camera Pos: [{cameraPosition.position.map(p => Number(p).toFixed(2)).join(', ')}]
              </Typography>
              <Typography variant="caption" sx={{ color: 'white', display: 'block' }}>
                Look At: [{cameraPosition.target.map(p => Number(p).toFixed(2)).join(', ')}]
              </Typography>
            </Box>
          </Paper>
        </Box>

        {/* Step Editor Panel - Right Side */}
        <Box sx={{ 
          flex: '1 1 25%', // Take 25% of available space
          height: '100%',
          minWidth: '320px', // Minimum width to prevent crushing
          maxWidth: '400px' // Maximum width to prevent too wide
        }}>
          <Paper sx={{ height: '100%', overflow: 'hidden', display: 'flex', flexDirection: 'column' }} elevation={3}>
            {/* Tabs Navigation */}
            <AppBar position="static" color="default" elevation={0} sx={{ flexShrink: 0 }}>
              <Tabs
                value={tabIndex}
                onChange={(e, newValue) => setTabIndex(newValue)}
                indicatorColor="primary"
                textColor="primary"
                variant="fullWidth"
                sx={{
                  '& .MuiTab-root': {
                    minHeight: '48px',
                    fontSize: '0.75rem',
                    padding: '6px 4px'
                  }
                }}
              >
                <Tab label="Basic" icon={<CameraIcon fontSize="small" />} iconPosition="top" />
                <Tab label="Hotspots" icon={<HotspotIcon fontSize="small" />} iconPosition="top" />
                <Tab label="Layers" icon={<LayersIcon fontSize="small" />} iconPosition="top" />
                <Tab label="AttachModel" icon={<AttachModelIcon fontSize="small" />} iconPosition="top" />
              </Tabs>
            </AppBar>

            {/* Tab Content - Scrollable */}
            <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
              {/* Tab 1: Basic Information */}
              <TabPanel value={tabIndex} index={0}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                  <TextField
                    label="Topic Title"
                    value={stepData.title}
                    onChange={(e) => setStepData({ ...stepData, title: e.target.value })}
                    fullWidth
                    required
                    size="small"
                  />
                  
                  <TextField
                    label="Content"
                    value={stepData.content}
                    onChange={(e) => setStepData({ ...stepData, content: e.target.value })}
                    multiline
                    rows={3}
                    fullWidth
                    required
                    placeholder="Detailed topic instructions. This will be displayed to the user."
                    size="small"
                  />

                  <TextField
                    label="Tags (comma separated)"
                    value={stepData.tags ? stepData.tags.join(', ') : ''}
                    onChange={(e) => setStepData({
                      ...stepData,
                      tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                    })}
                    fullWidth
                    placeholder="e.g. safety, installation, important"
                    size="small"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={stepData.autoPlay || false}
                        onChange={(e) => setStepData({ ...stepData, autoPlay: e.target.checked })}
                        size="small"
                      />
                    }
                    label="Auto-play actions when topic is loaded"
                  />
                  
                  <Divider sx={{ my: 1 }} />
                  
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>Camera Position</Typography>
                  
                  <Alert severity="info" sx={{ mb: 1, p: 1 }}>
                    <Typography variant="caption">
                      Navigate the 3D model to set the desired camera position. The position is automatically updated.
                    </Typography>
                  </Alert>
                  
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Typography variant="caption" sx={{ fontWeight: 500 }}>Position (x, y, z)</Typography>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <TextField
                        label="X"
                        value={Number(cameraPosition.position[0]).toFixed(2)}
                        onChange={(e) => {
                          const newPosition = [...cameraPosition.position];
                          newPosition[0] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            position: newPosition
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                      <TextField
                        label="Y"
                        value={Number(cameraPosition.position[1]).toFixed(2)}
                        onChange={(e) => {
                          const newPosition = [...cameraPosition.position];
                          newPosition[1] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            position: newPosition
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                      <TextField
                        label="Z"
                        value={Number(cameraPosition.position[2]).toFixed(2)}
                        onChange={(e) => {
                          const newPosition = [...cameraPosition.position];
                          newPosition[2] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            position: newPosition
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                    </Box>

                    <Typography variant="caption" sx={{ fontWeight: 500 }}>Target / Look At (x, y, z)</Typography>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <TextField
                        label="X"
                        value={Number(cameraPosition.target[0]).toFixed(2)}
                        onChange={(e) => {
                          const newTarget = [...cameraPosition.target];
                          newTarget[0] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            target: newTarget
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                      <TextField
                        label="Y"
                        value={Number(cameraPosition.target[1]).toFixed(2)}
                        onChange={(e) => {
                          const newTarget = [...cameraPosition.target];
                          newTarget[1] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            target: newTarget
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                      <TextField
                        label="Z"
                        value={Number(cameraPosition.target[2]).toFixed(2)}
                        onChange={(e) => {
                          const newTarget = [...cameraPosition.target];
                          newTarget[2] = parseFloat(e.target.value) || 0;
                          setCameraPosition({
                            ...cameraPosition,
                            target: newTarget
                          });
                        }}
                        type="number"
                        inputProps={{ step: 0.1 }}
                        fullWidth
                        size="small"
                      />
                    </Box>

                    <Button
                      variant="outlined"
                      size="small"
                      onClick={handleResetCamera}
                      sx={{ mt: 1 }}
                    >
                      Reset to Default
                    </Button>
                  </Box>
                </Box>
              </TabPanel>
              
              {/* Tab 2: Hotspots */}
              <TabPanel value={tabIndex} index={1}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6">
                      Hotspots ({hotspots.length})
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={() => {
                        setSnackbar({
                          open: true,
                          message: 'Click on the 3D model to place a hotspot',
                          severity: 'info'
                        });
                      }}
                    >
                      Add Hotspot
                    </Button>
                  </Box>
                  
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Click on the 3D model to add hotspots. Click on a hotspot to select and edit it.
                  </Alert>
                  
                  <List>
                    {hotspots.map((hotspot, index) => (
                      <ListItem 
                        key={hotspot.id || index}
                        button
                        selected={selectedHotspot?.id === hotspot.id}
                        onClick={() => setSelectedHotspot(hotspot)}
                      >
                        <ListItemText 
                          primary={hotspot.label}
                          secondary={`Position: [${hotspot.position.map(p => Number(p).toFixed(1)).join(', ')}]`}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => {
                              setHotspots(hotspots.filter(h => h.id !== hotspot.id));
                              if (selectedHotspot?.id === hotspot.id) {
                                setSelectedHotspot(null);
                              }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                  
                  {hotspots.length === 0 && (
                    <Box sx={{ textAlign: 'center', p: 3, bgcolor: 'background.paper' }}>
                      <Typography color="text.secondary">
                        No hotspots added yet. Click on the 3D model to add one.
                      </Typography>
                    </Box>
                  )}
                  
                  {selectedHotspot && (
                    <Card variant="outlined" sx={{ mt: 2 }}>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Edit Selected Hotspot
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
                          <TextField
                            label="Label"
                            value={selectedHotspot.label}
                            onChange={(e) => {
                              const updatedHotspots = hotspots.map(h => 
                                h.id === selectedHotspot.id 
                                  ? { ...h, label: e.target.value } 
                                  : h
                              );
                              setHotspots(updatedHotspots);
                              setSelectedHotspot({ ...selectedHotspot, label: e.target.value });
                            }}
                            fullWidth
                            size="small"
                          />
                          
                          <FormControl fullWidth size="small">
                            <InputLabel>Action Type</InputLabel>
                            <Select
                              value={selectedHotspot.action || 'info'}
                              label="Action Type"
                              onChange={(e) => {
                                const updatedHotspots = hotspots.map(h => 
                                  h.id === selectedHotspot.id 
                                    ? { ...h, action: e.target.value } 
                                    : h
                                );
                                setHotspots(updatedHotspots);
                                setSelectedHotspot({ ...selectedHotspot, action: e.target.value });
                              }}
                            >
                              <MenuItem value="info">Information</MenuItem>
                              <MenuItem value="highlight">Highlight</MenuItem>
                              <MenuItem value="popup">Popup</MenuItem>
                              <MenuItem value="animate">Animate</MenuItem>
                            </Select>
                          </FormControl>
                          
                          <TextField
                            label="Description"
                            value={selectedHotspot.description || ''}
                            onChange={(e) => {
                              const updatedHotspots = hotspots.map(h => 
                                h.id === selectedHotspot.id 
                                  ? { ...h, description: e.target.value } 
                                  : h
                              );
                              setHotspots(updatedHotspots);
                              setSelectedHotspot({ ...selectedHotspot, description: e.target.value });
                            }}
                            multiline
                            rows={3}
                            fullWidth
                            size="small"
                          />
                          
                          <Typography variant="subtitle2">Position (x, y, z)</Typography>
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <TextField
                              label="X"
                              type="number"
                              inputProps={{ step: 0.1 }}
                              value={Number(selectedHotspot.position[0]).toFixed(2)}
                              onChange={(e) => {
                                const newPosition = [...selectedHotspot.position];
                                newPosition[0] = parseFloat(e.target.value) || 0;
                                
                                const updatedHotspots = hotspots.map(h => 
                                  h.id === selectedHotspot.id 
                                    ? { ...h, position: newPosition } 
                                    : h
                                );
                                setHotspots(updatedHotspots);
                                setSelectedHotspot({ ...selectedHotspot, position: newPosition });
                              }}
                              fullWidth
                              size="small"
                            />
                            <TextField
                              label="Y"
                              type="number"
                              inputProps={{ step: 0.1 }}
                              value={Number(selectedHotspot.position[1]).toFixed(2)}
                              onChange={(e) => {
                                const newPosition = [...selectedHotspot.position];
                                newPosition[1] = parseFloat(e.target.value) || 0;
                                
                                const updatedHotspots = hotspots.map(h => 
                                  h.id === selectedHotspot.id 
                                    ? { ...h, position: newPosition } 
                                    : h
                                );
                                setHotspots(updatedHotspots);
                                setSelectedHotspot({ ...selectedHotspot, position: newPosition });
                              }}
                              fullWidth
                              size="small"
                            />
                            <TextField
                              label="Z"
                              type="number"
                              inputProps={{ step: 0.1 }}
                              value={Number(selectedHotspot.position[2]).toFixed(2)}
                              onChange={(e) => {
                                const newPosition = [...selectedHotspot.position];
                                newPosition[2] = parseFloat(e.target.value) || 0;
                                
                                const updatedHotspots = hotspots.map(h => 
                                  h.id === selectedHotspot.id 
                                    ? { ...h, position: newPosition } 
                                    : h
                                );
                                setHotspots(updatedHotspots);
                                setSelectedHotspot({ ...selectedHotspot, position: newPosition });
                              }}
                              fullWidth
                              size="small"
                            />
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  )}
                </Box>
              </TabPanel>
              
              {/* Tab 3: Layers */}
              <TabPanel value={tabIndex} index={2}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Typography variant="h6">
                    Model Layers
                  </Typography>
                  
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Manage the visibility of model layers. Click on a layer to toggle its visibility.
                  </Alert>
                  
                  {/* Layer controls - chỉ hiển thị khi có meshLayers */}
                  {meshLayers.length > 0 ? (
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {meshLayers.map((layer, index) => (
                        <LayerItem 
                          key={index}
                          layer={layer}
                          index={index}
                          isVisible={visibleLayers.has(layer.name)}
                          onVisibilityChange={handleLayerVisibilityChange}
                        />
                      ))}
                    </Box>
                  ) : (
                    <Box sx={{ textAlign: 'center', p: 3, bgcolor: 'background.paper' }}>
                      <Typography color="text.secondary">
                        No layers found in the model.
                      </Typography>
                    </Box>
                  )}
                </Box>
              </TabPanel>

              {/* Tab 4: Attach Models */}
              <TabPanel value={tabIndex} index={3}>
                <AttachModelManager
                  attachedModels={attachedModels}
                  setAttachedModels={setAttachedModels}
                  selectedAttachedModel={selectedAttachedModel}
                  setSelectedAttachedModel={setSelectedAttachedModel}
                  setSnackbar={setSnackbar}
                  transformMode={transformMode}
                  setTransformMode={setTransformMode}
                  attachedModelRefs={attachedModelRefs}
                  moduleId={moduleId}
                />
              </TabPanel>
            </Box>
          </Paper>
        </Box>
      </Box>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default StepEditor;