import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Snackbar,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
  Menu,
  ListItemIcon as MuiListItemIcon,
  Tooltip,
  Container,
  Skeleton, // Added Skeleton import here
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Assignment as AssignmentIcon,
  Quiz as QuizIcon,
  VideoLibrary as VideoIcon,
  PictureAsPdf as PdfIcon,
  ExpandMore as ExpandMoreIcon,
  School as SchoolIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  MoreVert as MoreVertIcon,
  ViewInAr as ViewInArIcon,
  Build as BuildIcon,
  ThreeDRotation as Model3DIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import adminModuleService from '../services/adminModuleService';
import Visual3DModuleBuilder from '../components/Visual3DModuleBuilder';
import ModuleCreator from '../components/ModuleCreator';
import StepTypeSelector from '../components/StepTypeSelector';
import StepsManager from '../components/StepsManager';
import QuizManager from '../components/QuizManager';
import Add3DModelDialog from '../components/Add3DModelDialog';
import { useNavigate } from 'react-router-dom';

function ModuleManagement() {
  const [modules, setModules] = useState([]);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openBuilderDialog, setOpenBuilderDialog] = useState(false);
  const [openCreatorDialog, setOpenCreatorDialog] = useState(false);
  const [openStepTypeDialog, setOpenStepTypeDialog] = useState(false);
  const [selectedModuleForStep, setSelectedModuleForStep] = useState(null);
  const [builderModule, setBuilderModule] = useState(null);
  const [moduleToDelete, setModuleToDelete] = useState(null);
  const [expandedModule, setExpandedModule] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedModuleForMenu, setSelectedModuleForMenu] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openStepsDialog, setOpenStepsDialog] = useState(false);
  const [openQuizDialog, setOpenQuizDialog] = useState(false);
  const [selectedModuleForSteps, setSelectedModuleForSteps] = useState(null);
  const [selectedModuleForQuiz, setSelectedModuleForQuiz] = useState(null);
  const [openAdd3DModelDialog, setOpenAdd3DModelDialog] = useState(false);
  const [selectedModuleFor3DModel, setSelectedModuleFor3DModel] = useState(null);
  const navigate = useNavigate();

  // Load data from API
  const [filteredModules, setFilteredModules] = useState([]);
  const [courseFilter, setCourseFilter] = useState(() => {
    // Load from session storage on init
    return sessionStorage.getItem('moduleManagement_courseFilter') || 'all';
  });
  const [searchTerm, setSearchTerm] = useState(() => {
    // Load from session storage on init
    return sessionStorage.getItem('moduleManagement_searchTerm') || '';
  });

  // Load data from API
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Load modules and courses in parallel
      const [modulesResponse, coursesResponse] = await Promise.all([
        adminModuleService.getModules(),
        adminModuleService.getCourses()
      ]);
      
      setModules(modulesResponse || []);
      setCourses(coursesResponse || []);
      
    } catch (error) {
      console.error('Error loading data:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to load data',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Effect to apply filters and save to session storage
  useEffect(() => {
    let filtered = [...modules];
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(module => 
        module.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        module.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply course filter
    if (courseFilter !== 'all') {
      if (courseFilter === 'standalone') {
        filtered = filtered.filter(module => !module.course);
      } else {
        filtered = filtered.filter(module => module.course === courseFilter);
      }
    }
    
    setFilteredModules(filtered);
    
    // Save filters to session storage
    sessionStorage.setItem('moduleManagement_courseFilter', courseFilter);
    sessionStorage.setItem('moduleManagement_searchTerm', searchTerm);
  }, [modules, searchTerm, courseFilter]);

  // Check for edit module request from CourseManagement
  useEffect(() => {
    const editModuleId = sessionStorage.getItem('moduleManagement_editModuleId');
    if (editModuleId && modules.length > 0) {
      // Find the module to edit
      const moduleToEdit = modules.find(m => m.id === editModuleId);
      if (moduleToEdit) {
        // Highlight the module briefly and show snackbar
        setSnackbar({
          open: true,
          message: `Found module: ${moduleToEdit.name}. Click edit to modify it.`,
          severity: 'success'
        });
        
        // Clear the session storage flag
        sessionStorage.removeItem('moduleManagement_editModuleId');
        
        // Auto-scroll to module if possible (basic implementation)
        setTimeout(() => {
          const moduleElements = document.querySelectorAll('[data-module-id]');
          const targetElement = Array.from(moduleElements).find(
            el => el.getAttribute('data-module-id') === editModuleId
          );
          if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            // Add temporary highlight
            targetElement.style.boxShadow = '0 0 20px rgba(25, 118, 210, 0.5)';
            setTimeout(() => {
              targetElement.style.boxShadow = '';
            }, 3000);
          }
        }, 500);
      }
    }
  }, [modules]);

  const handleOpenDeleteDialog = (module) => {
    setModuleToDelete(module);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setModuleToDelete(null);
  };

  const handleConfirmDelete = async () => {
    if (!moduleToDelete) return;

    try {
      setLoading(true);
      
      await adminModuleService.deleteModule(moduleToDelete.id);
      
      setSnackbar({
        open: true,
        message: 'Module deleted successfully!',
        severity: 'info'
      });

      // Reload data
      await loadData();
      handleCloseDeleteDialog();
      
    } catch (error) {
      console.error('Error deleting module:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to delete module',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (module) => {
    try {
      setLoading(true);
      
      await adminModuleService.updateModule(module.id, {
        isLocked: !module.isLocked
      });
      
      // Reload data
      await loadData();

      // Trigger frontend reload if available
      if (window.reloadModulesData) {
        window.reloadModulesData();
      }

      setSnackbar({
        open: true,
        message: `Module ${!module.isLocked ? 'locked' : 'unlocked'} successfully!`,
        severity: 'success'
      });
      
    } catch (error) {
      console.error('Error toggling module status:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to update module status',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const getCourseColor = (courseId) => {
    // Find course by ID
    const course = courses.find(cat => cat.id === courseId);
    if (!course) return 'default';
    
    // Simple color mapping based on course name
    const name = course.name.toLowerCase();
    if (name.includes('basic') || name.includes('introduction')) return 'primary';
    if (name.includes('installation')) return 'success';
    if (name.includes('maintenance')) return 'warning';
    if (name.includes('advanced')) return 'error';
    return 'default';
  };

  const getCourseName = (courseId) => {
    const course = courses.find(cat => cat.id === courseId);
    return course ? course.name : 'Standalone';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const handleOpenBuilder = (module) => {
    // Thay vì mở dialog, chuyển hướng đến trang StepEditor
    navigate(`/step-editor`, {
      state: {
        moduleId: module.id,
        moduleData: module
      }
    });
    handleCloseMenu();
  };

  const handleCloseBuilder = () => {
    setOpenBuilderDialog(false);
    setBuilderModule(null);
  };

  const handleSaveBuilder = async () => {
    try {
      setSnackbar({
        open: true,
        message: 'Module content saved successfully!',
        severity: 'success'
      });
      
      // Reload data to get updated counts
      await loadData();
      handleCloseBuilder();
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Error saving module content',
        severity: 'error'
      });
    }
  };

  const handleOpenMenu = (event, module) => {
    setAnchorEl(event.currentTarget);
    setSelectedModuleForMenu(module);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setSelectedModuleForMenu(null);
  };

  const handleViewModule = (module) => {
    setBuilderModule(module);
    setOpenBuilderDialog(true);
  };

  // Function to handle editing of steps & quiz
  const handleEditStepsQuiz = (module) => {
    navigate('/step-editor', { 
      state: { 
        moduleId: module.id, 
        moduleData: module 
      } 
    });
  };

  const handleOpenStepType = (module) => {
    setSelectedModuleForStep(module);
    setOpenStepTypeDialog(true);
  };

  const handleCloseStepType = () => {
    setOpenStepTypeDialog(false);
    setSelectedModuleForStep(null);
  };

  const handleOpenStepsManager = (module) => {
    setSelectedModuleForSteps(module);
    setOpenStepsDialog(true);
    handleCloseMenu();
  };

  const handleCloseStepsManager = () => {
    setOpenStepsDialog(false);
    setSelectedModuleForSteps(null);
    // Reload data to update step counts
    loadData();
  };

  const handleOpenQuizManager = (module) => {
    setSelectedModuleForQuiz(module);
    setOpenQuizDialog(true);
    handleCloseMenu();
  };

  const handleCloseQuizManager = () => {
    setOpenQuizDialog(false);
    setSelectedModuleForQuiz(null);
    // Reload data to update quiz counts
    loadData();
  };

  // Handle adding 3D model to existing module
  const handleAdd3DModel = (module) => {
    setSelectedModuleFor3DModel(module);
    setOpenAdd3DModelDialog(true);
    handleCloseMenu();
  };

  // Handle 3D model added successfully
  const handleModelAdded = async (result) => {
    console.log('✅ 3D model added to module:', result);
    setSnackbar({
      open: true,
      message: `3D Model added to "${selectedModuleFor3DModel?.name}" successfully!`,
      severity: 'success'
    });
    
    // Reload modules to reflect the changes
    await loadData();
    
    // Close dialog
    setOpenAdd3DModelDialog(false);
    setSelectedModuleFor3DModel(null);
  };

  // Handle 3D model dialog close
  const handleCloseAdd3DModelDialog = () => {
    setOpenAdd3DModelDialog(false);
    setSelectedModuleFor3DModel(null);
  };

  // Check if a module has 3D model
  const hasModel3D = (module) => {
    return module.has3DModel || module.modelUrl || module.model3DUrl;
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setCourseFilter('all');
  };

  // Get module count by course
  const getModuleCountByCourse = (courseId) => {
    if (courseId === 'all') return modules.length;
    if (courseId === 'standalone') return modules.filter(m => !m.course).length;
    return modules.filter(m => m.course === courseId).length;
  };

  return (
    <Box>
      {/* Header - Đơn giản hóa header với màu đen trắng */}
      <Paper 
        elevation={0}
        sx={{ 
          p: 3, 
          mb: 4, 
          borderBottom: '1px solid', 
          borderColor: 'divider',
          borderRadius: 0
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="500">
            Module Management
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<ViewInArIcon />}
              onClick={() => setOpenCreatorDialog(true)}
              disabled={loading}
              size="large"
              sx={{
                bgcolor: 'text.primary', 
                color: 'background.paper',
                '&:hover': {
                  bgcolor: 'text.secondary'
                }
              }}
            >
              CREATE MODULE
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Loading Indicator */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Filters */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6" fontWeight={600}>
            Filters & Search
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Chip 
              label={`Total: ${modules.length}`} 
              variant="outlined" 
              size="small"
            />
            <Chip 
              label={`Filtered: ${filteredModules.length}`} 
              color="primary" 
              variant="filled" 
              size="small"
            />
          </Box>
        </Box>
        
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              placeholder="Search by name or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              fullWidth
              size="medium"
              sx={{ 
                '& .MuiOutlinedInput-root': { 
                  borderRadius: 1,
                  backgroundColor: 'action.hover',
                  '&:hover': {
                    backgroundColor: 'action.selected',
                  },
                  '&.Mui-focused': {
                    backgroundColor: 'background.paper',
                  }
                } 
              }}
              InputProps={{
                startAdornment: (
                  <IconButton position="start" size="small">
                    <SearchIcon color="action" />
                  </IconButton>
                ),
              }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth size="medium">
              <InputLabel>Filter by Course</InputLabel>
              <Select
                value={courseFilter}
                label="Filter by Course"
                onChange={(e) => setCourseFilter(e.target.value)}
                sx={{ 
                  borderRadius: 1,
                  backgroundColor: 'action.hover',
                  '&:hover': {
                    backgroundColor: 'action.selected',
                  },
                  '&.Mui-focused': {
                    backgroundColor: 'background.paper',
                  }
                }}
              >
                <MenuItem value="all">
                  All Courses ({getModuleCountByCourse('all')})
                </MenuItem>
                <MenuItem value="standalone">
                  Standalone Modules ({getModuleCountByCourse('standalone')})
                </MenuItem>
                {courses.map((course) => (
                  <MenuItem key={course.id} value={course.id}>
                    {course.name} ({getModuleCountByCourse(course.id)})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            {(searchTerm || courseFilter !== 'all') && (
              <Button
                size="medium"
                startIcon={<ClearIcon />}
                onClick={clearFilters}
                variant="outlined"
                color="warning"
                sx={{ 
                  borderRadius: 1,
                  height: 56, // Match TextField height
                  '&:hover': {
                    backgroundColor: 'warning.light'
                  }
                }}
              >
                Clear
              </Button>
            )}
          </Grid>
        </Grid>
      </Paper>

      {/* Modules Grid - Adjusted to eliminate extra space on the right */}
      <Box sx={{ px: 3 }}> {/* Added padding container instead of Container component */}
        <Grid container spacing={3} sx={{ width: '100%' }}>
          {loading ? (
            // Skeleton loading state
            Array.from(new Array(6)).map((_, index) => (
              <Grid item xs={12} sm={6} md={4} key={`skeleton-${index}`}>
                <Card 
                  sx={{ 
                    height: 360, 
                    width: 340,
                    display: 'flex', 
                    flexDirection: 'column',
                    borderRadius: 1,
                  }}
                >
                  <Box sx={{ p: 3, flexGrow: 1 }}>
                    <Skeleton variant="rectangular" height={24} width="80%" sx={{ mb: 2 }} />
                    <Skeleton variant="rectangular" height={60} sx={{ mb: 2 }} />
                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      <Skeleton variant="rectangular" height={24} width={80} sx={{ borderRadius: 4 }} />
                      <Skeleton variant="rectangular" height={24} width={80} sx={{ borderRadius: 4 }} />
                      <Skeleton variant="rectangular" height={24} width={90} sx={{ borderRadius: 4 }} />
                    </Box>
                    <Skeleton variant="text" height={20} width="60%" />
                  </Box>
                  <Skeleton variant="rectangular" height={1} sx={{ width: '100%' }} />
                  <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between' }}>
                    <Skeleton variant="rectangular" height={30} width={100} />
                    <Skeleton variant="rectangular" height={30} width={70} />
                  </Box>
                </Card>
              </Grid>
            ))
          ) : (
            // Actual modules display - Use filteredModules instead of modules
            filteredModules.map((module) => (
              <Grid item xs={12} sm={6} md={4} key={module.id}>
                <Card 
                  data-module-id={module.id}
                  sx={{ 
                    height: 360, // Chiều cao cố định cho tất cả các card
                    width: 340,
                    display: 'flex', 
                    flexDirection: 'column',
                    borderRadius: 1,
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                      transform: 'translateY(-2px)',
                    },
                    overflow: 'hidden' // Ngăn nội dung tràn ra ngoài
                  }}
                >
                  {/* Content area với chiều cao cố định */}
                  <CardContent 
                    sx={{ 
                      display: 'flex', 
                      flexDirection: 'column', 
                      p: 3, 
                      height: 294, // Chiều cao cố định cho phần nội dung
                      '&:last-child': { pb: 3 } // Ghi đè padding-bottom mặc định của CardContent
                    }}
                  >
                    {/* Header area với chiều cao cố định */}
                    <Box 
                      sx={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        mb: 2, 
                        height: 32, // Chiều cao cố định cho header
                        alignItems: 'flex-start'
                      }}
                    >
                      <Typography 
                        variant="h6" 
                        component="h2" 
                        fontWeight="500"
                        sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: 'calc(100% - 90px)' // Để lại khoảng trống cho chip
                        }}
                      >
                        {module.name}
                      </Typography>
                      <Chip
                        label={module.isLocked ? 'Coming Soon' : 'Available'}
                        size="small"
                        clickable
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleStatus(module);
                        }}
                        sx={{
                          bgcolor: module.isLocked ? 'rgba(0, 0, 0, 0.08)' : 'rgba(0, 0, 0, 0.05)',
                          fontWeight: 500,
                          cursor: 'pointer',
                      
                        }}
                      />
                    </Box>

                    {/* Description area với chiều cao cố định */}
                    <Box sx={{ height: 60, mb: 2 }}>
                      <Typography 
                        variant="body2" 
                        color="text.secondary" 
                        sx={{ 
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          lineHeight: '20px',
                          height: '100%'
                        }}
                      >
                        {module.description}
                      </Typography>
                    </Box>

                    {/* Chips area với chiều cao cố định */}
                    <Box sx={{ 
                      display: 'flex', 
                      flexWrap: 'wrap', 
                      gap: 1, 
                      mb: 2,
                      height: 32, // Chiều cao cố định cho chips
                      overflow: 'hidden'
                    }}>
                      <Chip
                        icon={<AssignmentIcon sx={{ fontSize: '0.8rem' }} />}
                        label={`${module.actualStepsCount || 0} Topics`}
                        size="small"
                        variant="outlined"
                        sx={{ 
                          borderColor: 'divider', 
                          color: 'text.primary',
                          '& .MuiChip-icon': { color: 'inherit' }
                        }}
                      />
                      <Chip
                        icon={<QuizIcon sx={{ fontSize: '0.8rem' }} />}
                        label={`${module.actualQuizCount || 0} Quiz`}
                        size="small"
                        variant="outlined"
                        sx={{ 
                          borderColor: 'divider', 
                          color: 'text.primary',
                          '& .MuiChip-icon': { color: 'inherit' }
                        }}
                      />
                      {module.course && (
                        <Chip
                          label={getCourseName(module.course)}
                          size="small"
                          variant="outlined"
                          sx={{ 
                            borderColor: 'divider', 
                            color: 'text.primary'
                          }}
                        />
                      )}
                    </Box>

                    {/* Date area luôn nằm ở dưới cùng của nội dung */}
                    <Box sx={{ 
                      mt: 'auto',
                      height: 20 // Chiều cao cố định cho date
                    }}>
                      <Typography variant="caption" color="text.secondary">
                        Updated: {formatDate(module.lastUpdated)}
                      </Typography>
                    </Box>
                  </CardContent>

                  <Divider sx={{ flexShrink: 0 }} /> {/* Ngăn divider co lại */}
                  
                  {/* Actions area với chiều cao cố định */}
                  <CardActions 
                    sx={{ 
                      justifyContent: 'space-between', 
                      px: 2, 
                      py: 1, 
                      height: 52, // Chiều cao cố định cho phần action
                      flexShrink: 0 // Ngăn co lại khi nội dung nhiều
                    }}
                  >
                    <Box>
                      {/* Conditional rendering based on 3D model availability */}
                      {module.has3DModel || module.modelUrl ? (
                        <Tooltip title="View Module">
                          <IconButton 
                            size="small" 
                            sx={{ color: 'text.secondary' }}
                            onClick={() => handleViewModule(module)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      ) : (
                        <Tooltip title="Add 3D Model">
                          <IconButton 
                            size="small" 
                            sx={{ 
                              color: 'primary.main',
                              bgcolor: 'primary.light',
                              '&:hover': {
                                bgcolor: 'primary.main',
                                color: 'white'
                              }
                            }}
                            onClick={() => handleAdd3DModel(module)}
                          >
                            <Model3DIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      
                      <Tooltip title="Add topic">
                        <IconButton 
                          size="small" 
                          sx={{ 
                            color: 'text.primary',
                            bgcolor: 'background.default',
                            '&:hover': {
                              bgcolor: 'action.hover',
                            }
                          }}
                          onClick={() => handleOpenStepType(module)}
                          disabled={loading}
                        >
                          <ViewInArIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                    <Box>
                      <Tooltip title="More Actions">
                        <IconButton 
                          size="small"
                          onClick={(e) => handleOpenMenu(e, module)}
                          disabled={loading}
                          sx={{ color: 'text.secondary' }}
                        >
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Module">
                        <IconButton 
                          size="small" 
                          sx={{ color: 'text.secondary' }}
                          onClick={() => handleOpenDeleteDialog(module)}
                          disabled={loading}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </CardActions>
                </Card>
              </Grid>
            ))
          )}
        </Grid>
      </Box>

      {/* Empty State - Đơn giản hóa trạng thái trống */}
      {!loading && modules.length === 0 && (
        <Paper 
          elevation={0}
          sx={{ 
            p: 6, 
            textAlign: 'center', 
            mt: 4, 
            bgcolor: 'background.default', 
            border: '1px dashed',
            borderColor: 'divider'
          }}
        >
          <SchoolIcon sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No modules found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Create your first module to get started
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
            <Button
              variant="contained"
              startIcon={<ViewInArIcon />}
              onClick={() => setOpenCreatorDialog(true)}
              size="large"
              sx={{
                bgcolor: 'text.primary', 
                color: 'background.paper',
                '&:hover': {
                  bgcolor: 'text.secondary'
                }
              }}
            >
              Create Module
            </Button>
          </Box>
        </Paper>
      )}

      {/* Action Menu - Updated with new handlers */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
        PaperProps={{
          sx: { 
            minWidth: 200,
            boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
            borderRadius: 1
          }
        }}
      >
        {/* <MenuItem onClick={() => handleOpenBuilder(selectedModuleForMenu)}>
          <MuiListItemIcon>
            <ViewInArIcon fontSize="small" sx={{ color: 'text.primary' }} />
          </MuiListItemIcon>
          <Box>
            <Typography variant="body2">
              3D Viewer
            </Typography>
            <Typography variant="caption" color="text.secondary">
              View 3D model only
            </Typography>
          </Box>
        </MenuItem> */}
        <Divider />
        <MenuItem onClick={() => handleOpenStepsManager(selectedModuleForMenu)}>
          <MuiListItemIcon>
            <AssignmentIcon fontSize="small" sx={{ color: 'text.primary' }} />
          </MuiListItemIcon>
          <Box>
            <Typography variant="body2">
              Manage Topics ({selectedModuleForMenu?.actualStepsCount || 0})
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Edit, add, delete topics
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={() => handleOpenQuizManager(selectedModuleForMenu)}>
          <MuiListItemIcon>
            <QuizIcon fontSize="small" sx={{ color: 'text.primary' }} />
          </MuiListItemIcon>
          <Box>
            <Typography variant="body2">
              Manage Quiz ({selectedModuleForMenu?.actualQuizCount || 0})
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Edit, add, delete questions
            </Typography>
          </Box>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          navigator.clipboard.writeText(selectedModuleForMenu?.id);
          setSnackbar({
            open: true,
            message: 'Module ID copied to clipboard',
            severity: 'info'
          });
          handleCloseMenu();
        }}>
          <MuiListItemIcon>
            <SchoolIcon fontSize="small" sx={{ color: 'text.primary' }} />
          </MuiListItemIcon>
          Copy Module ID
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-confirmation-dialog"
        PaperProps={{
          sx: {
            borderRadius: 1,
            width: 400
          }
        }}
      >
        <DialogTitle id="delete-confirmation-dialog" sx={{ borderBottom: '1px solid', borderColor: 'divider', py: 2 }}>
          Confirm Deletion
        </DialogTitle>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          <Typography variant="body1" gutterBottom>
            Are you sure you want to delete the module "{moduleToDelete?.name}"?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            This action cannot be undone. All related topics, quiz questions, and progress data will be permanently deleted.
          </Typography>
          {moduleToDelete?.userProgressCount > 0 && (
            <Alert severity="warning" sx={{ mt: 2, borderRadius: 1 }}>
              This module has {moduleToDelete.userProgressCount} users with progress data.
            </Alert>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Button 
            onClick={handleCloseDeleteDialog} 
            disabled={loading}
            sx={{ color: 'text.secondary' }}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleConfirmDelete} 
            variant="contained"
            disabled={loading}
            sx={{
              bgcolor: 'text.primary', 
              color: 'background.paper',
              '&:hover': {
                bgcolor: 'text.secondary'
              }
            }}
          >
            {loading ? 'Deleting...' : 'Delete Module'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 3D Module Creator Dialog */}
      <ModuleCreator
        open={openCreatorDialog}
        onClose={() => setOpenCreatorDialog(false)}
        courses={courses}
        onModuleCreated={(newModule) => {
          console.log('✅ New 3D module created:', newModule);
          setSnackbar({
            open: true,
            message: `3D Module "${newModule.name}" created successfully!`,
            severity: 'success'
          });
          loadData(); // Reload modules list
          setOpenCreatorDialog(false);
        }}
      />

      {/* 3D Module Builder Dialog */}
      <Dialog
        open={openBuilderDialog}
        onClose={handleCloseBuilder}
        maxWidth={false}
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            width: '95vw',
            height: '90vh',
            maxWidth: 'none',
            maxHeight: 'none',
            borderRadius: 1
          },
        }}
      >
        <DialogContent sx={{ p: 0, height: '100%' }}>
          {builderModule && (
            <Visual3DModuleBuilder
              moduleId={builderModule.id}
              moduleName={builderModule.name}
              onSave={handleSaveBuilder}
              onCancel={handleCloseBuilder}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Step Type Selector Dialog */}
      <StepTypeSelector
        open={openStepTypeDialog}
        onClose={handleCloseStepType}
        moduleData={selectedModuleForStep}
        onSelectType={(stepType) => {
          console.log('Selected topic type:', stepType, 'for module:', selectedModuleForStep?.name);
          
          // Navigate to appropriate editor based on step type
          switch (stepType) {
            case '3d-model':
              navigate('/step-editor', {
                state: {
                  moduleId: selectedModuleForStep.id,
                  moduleData: selectedModuleForStep,
                  stepType: '3d-model'
                }
              });
              break;
            case 'video':
              // For now, we'll use the same step editor but pass the type
              // Later you can create a specific VideoStepEditor
              navigate('/step-editor-video', {
                state: {
                  moduleId: selectedModuleForStep.id,
                  moduleData: selectedModuleForStep,
                  stepType: 'video'
                }
              });
              break;
            case 'image':
              // For now, we'll use the same step editor but pass the type
              // Later you can create a specific ImageStepEditor
              navigate('/step-editor-image', {
                state: {
                  moduleId: selectedModuleForStep.id,
                  moduleData: selectedModuleForStep,
                  stepType: 'image'
                }
              });
              break;
            default:
              console.warn('Unknown topic type:', stepType);
          }
          
          setSnackbar({
            open: true,
            message: `Opening ${stepType} topic editor...`,
            severity: 'info'
          });
        }}
      />

      {/* Steps Manager Dialog */}
      <Dialog
        open={openStepsDialog}
        onClose={handleCloseStepsManager}
        maxWidth={false}
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            width: '90vw',
            height: '85vh',
            maxWidth: 'none',
            maxHeight: 'none',
            borderRadius: 1
          },
        }}
      >
        <DialogContent sx={{ p: 0, height: '100%' }}>
          {selectedModuleForSteps && (
            <StepsManager
              moduleId={selectedModuleForSteps.id}
              moduleName={selectedModuleForSteps.name}
              moduleData={selectedModuleForSteps}
              onClose={handleCloseStepsManager}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Quiz Manager Dialog */}
      <Dialog
        open={openQuizDialog}
        onClose={handleCloseQuizManager}
        maxWidth={false}
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            width: '90vw',
            height: '85vh',
            maxWidth: 'none',
            maxHeight: 'none',
            borderRadius: 1
          },
        }}
      >
        <DialogContent sx={{ p: 0, height: '100%' }}>
          {selectedModuleForQuiz && (
            <QuizManager
              moduleId={selectedModuleForQuiz.id}
              moduleName={selectedModuleForQuiz.name}
              onClose={handleCloseQuizManager}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          severity={snackbar.severity} 
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          sx={{ 
            borderRadius: 1,
            '& .MuiAlert-icon': {
              color: 'text.primary'
            }
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Add 3D Model Dialog */}
      <Add3DModelDialog
        open={openAdd3DModelDialog}
        onClose={handleCloseAdd3DModelDialog}
        moduleData={selectedModuleFor3DModel}
        onModelAdded={handleModelAdded}
      />
    </Box>
  );
}

export default ModuleManagement;