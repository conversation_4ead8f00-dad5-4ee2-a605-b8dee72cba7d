import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  AppBar,
  Toolbar,
  IconButton,
  Tabs,
  Tab,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  FormControlLabel,
  CircularProgress,
  Snackbar,
  Alert,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
  Fab,
  LinearProgress,
} from '@mui/material';
import {
  ArrowBack,
  Save,
  Add as AddIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
  Notes as AnnotationIcon,
  Timeline as TimelineIcon,
  LiveHelp as TutorialIcon,
  CloudUpload,
  ZoomIn,
  ZoomOut,
  CenterFocusWeak,
} from '@mui/icons-material';

import adminModuleService from '../services/adminModuleService';
import { convertToSignedUrl } from '../utils/wasabiHelper';

// Tab Panel Component
function TabPanel(props) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`edit-image-tabpanel-${index}`}
      aria-labelledby={`edit-image-tab-${index}`}
      style={{ padding: '16px 0' }}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

// Image Viewer Component with edit capabilities
function EditImageViewer({ 
  imageUrl, 
  imageAnnotations = [], 
  zoom = 1, 
  onZoomChange, 
  onAnnotationClick, 
  onImageClick,
  showAnnotations = true, // Thêm prop để control hiển thị annotations
  enableDrag = true // Thêm prop để control drag functionality
}) {
  const containerRef = useRef(null);
  const [dragStart, setDragStart] = useState(null);
  const [offset, setOffset] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);

  const handleMouseDown = (e) => {
    if (e.button !== 0 || !enableDrag) return; // Chỉ handle drag khi enableDrag = true
    setDragStart({ x: e.clientX - offset.x, y: e.clientY - offset.y });
    setIsDragging(true);
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !dragStart || !enableDrag) return;
    setOffset({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setDragStart(null);
  };

  const handleImageClick = (e) => {
    if (isDragging || !onImageClick) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    
    onImageClick({ x, y });
  };

  useEffect(() => {
    if (isDragging && enableDrag) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragStart, enableDrag]);

  return (
    <Box 
      ref={containerRef}
      sx={{ 
        position: 'relative', 
        width: '100%', 
        height: '100%',
        overflow: 'hidden',
        cursor: !enableDrag ? 'crosshair' : (isDragging ? 'grabbing' : 'grab')
      }}
      onMouseDown={handleMouseDown}
    >
      {imageUrl ? (
        <>
          <img
            src={imageUrl}
            alt="Topic Image"
            style={{
              position: 'absolute',
              transform: `translate(${offset.x}px, ${offset.y}px) scale(${zoom})`,
              transformOrigin: 'center center',
              maxWidth: 'none',
              maxHeight: 'none',
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              userSelect: 'none',
              pointerEvents: isDragging ? 'none' : 'auto'
            }}
            onClick={handleImageClick}
            draggable={false}
          />
          
          {/* Image Annotations Overlay - chỉ hiển thị khi showAnnotations = true */}
          {showAnnotations && imageAnnotations.map((annotation, index) => (
            <Box
              key={annotation.id || index}
              sx={{
                position: 'absolute',
                left: `${annotation.position?.x || 50}%`,
                top: `${annotation.position?.y || 50}%`,
                transform: `translate(-50%, -50%) scale(${1/zoom})`,
                transformOrigin: 'center center',
                zIndex: 10,
                pointerEvents: 'auto'
              }}
            >
              <Button
                variant="contained"
                size="small"
                color={annotation.style?.color || 'primary'}
                onClick={(e) => {
                  e.stopPropagation();
                  onAnnotationClick?.(annotation);
                }}
                sx={{
                  minWidth: 'auto',
                  borderRadius: '50%',
                  width: 32,
                  height: 32,
                  fontSize: '12px',
                  fontWeight: 'bold',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
                  '&:hover': {
                    transform: 'scale(1.1)',
                    transition: 'transform 0.2s'
                  }
                }}
              >
                {index + 1}
              </Button>
            </Box>
          ))}
        </>
      ) : (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: '#f5f5f5',
            border: '2px dashed #ccc',
          }}
        >
          <CloudUpload sx={{ fontSize: 60, color: '#999', mb: 2 }} />
          <Typography variant="h6" color="textSecondary">
            No Image Available
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Upload a new image to replace
          </Typography>
        </Box>
      )}
    </Box>
  );
}

function EditImageStep() {
  const location = useLocation();
  const navigate = useNavigate();
  const { moduleId, stepId, moduleData, stepData: existingStepData } = location.state || {};
  
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [updateProgress, setUpdateProgress] = useState(0);
  const [imageUrl, setImageUrl] = useState('');
  const [tabIndex, setTabIndex] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [zoom, setZoom] = useState(1);
  const fileInputRef = useRef(null);

  // Step data state
  const [stepData, setStepData] = useState({
    title: '',
    content: '',
    order: 1,
    autoPlay: false,
    tags: [],
    stepType: 'image'
  });

  // Image-specific state
  const [imageFile, setImageFile] = useState(null);
  const [imageAnnotations, setImageAnnotations] = useState([]);
  const [selectedAnnotation, setSelectedAnnotation] = useState(null);

  // Load existing step data for editing
  useEffect(() => {
    if (!moduleId || !stepId) {
      navigate('/modules');
      return;
    }
    
    const loadStepData = async () => {
      setLoading(true);
      try {
        let stepResponse = null;
        
        // Use existing stepData from navigation state if available
        if (existingStepData) {
          stepResponse = existingStepData;
          console.log('🔄 Using topic data from navigation state:', stepResponse);
        } else {
          // Fallback to API call if no stepData provided
          stepResponse = await adminModuleService.getStep(moduleId, stepId);
          console.log('🔄 Loaded topic data from API:', stepResponse);
        }
        
        if (stepResponse) {
          // Set basic step data
          setStepData({
            title: stepResponse.title || '',
            content: stepResponse.content || '',
            order: stepResponse.order || 1,
            autoPlay: stepResponse.autoPlay || false,
            tags: stepResponse.tags || [],
            stepType: 'image'
          });
          
          // Set image URL
          if (stepResponse.imageUrl) {
            const signedUrl = convertToSignedUrl(stepResponse.imageUrl);
            setImageUrl(signedUrl);
          }
          
          // Set image annotations
          if (stepResponse.imageAnnotations && Array.isArray(stepResponse.imageAnnotations)) {
            setImageAnnotations(stepResponse.imageAnnotations);
          }
        }
      } catch (error) {
        console.error('❌ Error loading topic data:', error);
        setSnackbar({
          open: true,
          message: `Error loading topic data: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };
    
    loadStepData();
  }, [moduleId, stepId, existingStepData, navigate]);

  // Handle image file selection for replacement
  const handleImageFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      setSnackbar({
        open: true,
        message: 'Please select a valid image file (JPEG, PNG, GIF, WebP)',
        severity: 'error'
      });
      return;
    }

    setImageFile(file);
    const newImageUrl = URL.createObjectURL(file);
    setImageUrl(newImageUrl);
    
    console.log('🖼️ New image file selected:', file.name);
  };

  // Handle image annotation click on image
  const handleImageClick = (position) => {
    if (tabIndex !== 1) return;

    const newAnnotation = {
      id: `annotation_${Date.now()}`,
      position: position,
      title: `Annotation ${imageAnnotations.length + 1}`,
      description: '',
      action: 'info',
      style: {
        color: 'primary',
        size: 'medium'
      }
    };

    setImageAnnotations([...imageAnnotations, newAnnotation]);
    setSelectedAnnotation(newAnnotation);
    
    setSnackbar({
      open: true,
      message: 'New annotation added! Edit it in the panel.',
      severity: 'success'
    });
  };

  // Handle zoom controls
  const handleZoomIn = () => setZoom(prevZoom => Math.min(prevZoom * 1.2, 5));
  const handleZoomOut = () => setZoom(prevZoom => Math.max(prevZoom / 1.2, 0.1));
  const handleZoomReset = () => setZoom(1);

  // Handle update step
  const handleUpdateStep = async () => {
    try {
      setUpdating(true);
      setUpdateProgress(0);

      if (!stepData.title.trim()) {
        throw new Error('Topic title is required');
      }
      if (!stepData.content.trim()) {
        throw new Error('Topic content is required');
      }

      console.log('🔄 Updating image topic...');

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUpdateProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      let result;

      if (imageFile) {
        // If there's a new image file, upload it
        const formData = new FormData();
        formData.append('image', imageFile);
        formData.append('stepData', JSON.stringify({
          ...stepData,
          imageAnnotations: imageAnnotations
        }));

        result = await adminModuleService.updateStepWithImage(moduleId, stepId, formData);
      } else {
        // No new image file, use regular update
        const completeStepData = {
          ...stepData,
          imageUrl: imageUrl,
          imageAnnotations: imageAnnotations
        };

        result = await adminModuleService.updateStep(moduleId, stepId, completeStepData);
      }

      clearInterval(progressInterval);
      setUpdateProgress(100);

      console.log('✅ Image topic updated successfully:', result);

      setSnackbar({
        open: true,
        message: 'Image topic updated successfully!',
        severity: 'success'
      });
      
      setTimeout(() => {
        navigate('/modules');
      }, 1500);

    } catch (error) {
      console.error('❌ Error updating image topic:', error);
      setSnackbar({
        open: true,
        message: `Error updating topic: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setUpdating(false);
      setUpdateProgress(0);
    }
  };

  if (loading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column',
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh',
        gap: 2
      }}>
        <CircularProgress size={60} />
        <Typography variant="h6" color="text.secondary">
          Loading Image Topic Data...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ height: 'calc(100vh - 88px)', overflow: 'hidden' }}>
      {/* Header/Toolbar */}
      <Paper sx={{ mb: 2 }} elevation={2}>
        <Toolbar>
          <IconButton edge="start" color="inherit" onClick={() => navigate('/modules')}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" sx={{ ml: 2, flex: 1 }}>
            Edit Image Topic: {stepData.title}
          </Typography>
          <Button 
            variant="contained"
            color="warning"
            startIcon={<Save />}
            onClick={handleUpdateStep}
            disabled={loading || updating || !stepData.title || !stepData.content}
          >
            Update Topic
          </Button>
        </Toolbar>
      </Paper>

      {/* Main Content */}
      <Box sx={{ 
        display: 'flex', 
        height: 'calc(100% - 80px)', 
        gap: 1,
        px: 2
      }}>
        {/* Image Preview Panel - Left Side */}
        <Box sx={{ 
          flex: '1 1 75%',
          height: '100%',
          minWidth: 0
        }}>
          <Paper sx={{ height: '100%', position: 'relative', overflow: 'hidden' }} elevation={3}>
            <EditImageViewer
              imageUrl={imageUrl}
              imageAnnotations={imageAnnotations}
              zoom={zoom}
              onZoomChange={setZoom}
              onAnnotationClick={setSelectedAnnotation}
              onImageClick={handleImageClick}
              showAnnotations={tabIndex === 1} // Chỉ hiển thị annotations khi ở tab Annotations (index 1)
              enableDrag={tabIndex !== 1} // Disable drag khi ở tab Annotations để dễ click thêm annotations
            />
            
            {/* Image Controls Toolbar */}
            <Box sx={{ 
              position: 'absolute', 
              top: 16, 
              left: 16,
              display: 'flex',
              flexDirection: 'column',
              gap: 2
            }}>
              <Tooltip title="Replace Image">
                <Fab 
                  size="small" 
                  color="primary" 
                  onClick={() => fileInputRef.current?.click()}
                >
                  <ImageIcon />
                </Fab>
              </Tooltip>
              <Tooltip title="Add Annotation (Click on image)">
                <Fab size="small" color="secondary">
                  <AnnotationIcon />
                </Fab>
              </Tooltip>
            </Box>
            
            {/* Zoom Controls */}
            <Box sx={{ 
              position: 'absolute', 
              top: 16, 
              right: 16,
              display: 'flex',
              flexDirection: 'column',
              gap: 1
            }}>
              <Tooltip title="Zoom In">
                <Fab size="small" onClick={handleZoomIn}>
                  <ZoomIn />
                </Fab>
              </Tooltip>
              <Tooltip title="Zoom Out">
                <Fab size="small" onClick={handleZoomOut}>
                  <ZoomOut />
                </Fab>
              </Tooltip>
              <Tooltip title="Reset Zoom">
                <Fab size="small" onClick={handleZoomReset}>
                  <CenterFocusWeak />
                </Fab>
              </Tooltip>
            </Box>
            
            {/* Image Info */}
            <Box sx={{ 
              position: 'absolute', 
              bottom: 16, 
              left: 16, 
              right: 16,
              background: 'rgba(0,0,0,0.5)',
              borderRadius: 1,
              p: 1,
              color: 'white',
              fontSize: '12px'
            }}>
              <Typography variant="caption" sx={{ color: 'white', display: 'block' }}>
                Zoom: {Math.round(zoom * 100)}% | Annotations: {imageAnnotations.length}
              </Typography>
              {imageFile && (
                <Typography variant="caption" sx={{ color: 'yellow', display: 'block' }}>
                  ⚠️ New image selected - will replace current image on save
                </Typography>
              )}
            </Box>

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              style={{ display: 'none' }}
              onChange={handleImageFileSelect}
            />
          </Paper>
        </Box>

        {/* Step Editor Panel - Right Side */}
        <Box sx={{ 
          flex: '1 1 25%',
          height: '100%',
          minWidth: '320px',
          maxWidth: '400px'
        }}>
          <Paper sx={{ height: '100%', overflow: 'hidden', display: 'flex', flexDirection: 'column' }} elevation={3}>
            {/* Tabs Navigation */}
            <AppBar position="static" color="default" elevation={0} sx={{ flexShrink: 0 }}>
              <Tabs
                value={tabIndex}
                onChange={(e, newValue) => setTabIndex(newValue)}
                indicatorColor="primary"
                textColor="primary"
                variant="fullWidth"
                sx={{
                  '& .MuiTab-root': {
                    minHeight: '48px',  
                    fontSize: '0.75rem',
                    padding: '6px 4px'
                  }
                }}
              >
                <Tab label="Basic" icon={<ImageIcon fontSize="small" />} iconPosition="top" />
                <Tab label="Annotations" icon={<AnnotationIcon fontSize="small" />} iconPosition="top" />
                {/* Actions and Tutorial tabs removed - these features are no longer supported */}
              </Tabs>
            </AppBar>

            {/* Tab Content */}
            <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
              {/* Tab 1: Basic Information */}
              <TabPanel value={tabIndex} index={0}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                  <TextField
                    label="Topic Title"
                    value={stepData.title}
                    onChange={(e) => setStepData({ ...stepData, title: e.target.value })}
                    fullWidth
                    required
                    size="small"
                  />
                  
                  <TextField
                    label="Content"
                    value={stepData.content}
                    onChange={(e) => setStepData({ ...stepData, content: e.target.value })}
                    multiline
                    rows={3}
                    fullWidth
                    required
                    placeholder="Detailed step instructions for users"
                    size="small"
                  />

                  <Grid container spacing={1}>
                    <Grid item xs={6}>
                      <TextField
                        label="Order"
                        type="number"
                        value={stepData.order}
                        onChange={(e) => setStepData({ ...stepData, order: parseInt(e.target.value) || 1 })}
                        fullWidth
                        size="small"
                        InputProps={{
                          startAdornment: <InputAdornment position="start">#</InputAdornment>,
                        }}
                      />
                    </Grid>
                  </Grid>

                  <TextField
                    label="Tags (comma separated)"
                    value={stepData.tags ? stepData.tags.join(', ') : ''}
                    onChange={(e) => setStepData({
                      ...stepData,
                      tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                    })}
                    fullWidth
                    placeholder="e.g. image, diagram, important"
                    size="small"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={stepData.autoPlay || false}
                        onChange={(e) => setStepData({ ...stepData, autoPlay: e.target.checked })}
                        size="small"
                      />
                    }
                    label="Auto-highlight annotations on load"
                  />
                  
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="caption">
                      💡 Editing existing image topic. Click on image to add new annotations.
                    </Typography>
                  </Alert>
                </Box>
              </TabPanel>
              
              {/* Tab 2: Image Annotations - Same as create but with editing context */}
              <TabPanel value={tabIndex} index={1}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6">
                      Annotations ({imageAnnotations.length})
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={() => {
                        const newAnnotation = {
                          id: `annotation_${Date.now()}`,
                          position: { x: 50, y: 50 },
                          title: `Annotation ${imageAnnotations.length + 1}`,
                          description: '',
                          action: 'info',
                          style: { color: 'primary', size: 'medium' }
                        };
                        setImageAnnotations([...imageAnnotations, newAnnotation]);
                        setSelectedAnnotation(newAnnotation);
                      }}
                    >
                      Add
                    </Button>
                  </Box>
                  
                  <List>
                    {imageAnnotations.map((annotation, index) => (
                      <ListItem 
                        key={annotation.id || index}
                        button
                        selected={selectedAnnotation?.id === annotation.id}
                        onClick={() => setSelectedAnnotation(annotation)}
                      >
                        <ListItemText 
                          primary={annotation.title}
                          secondary={`Position: ${Math.round(annotation.position.x)}%, ${Math.round(annotation.position.y)}%`}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => {
                              setImageAnnotations(imageAnnotations.filter(a => a.id !== annotation.id));
                              if (selectedAnnotation?.id === annotation.id) {
                                setSelectedAnnotation(null);
                              }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                  
                  {/* Edit annotation form - similar to create but for editing */}
                  {selectedAnnotation && (
                    <Card variant="outlined" sx={{ mt: 2 }}>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Edit Annotation
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
                          <TextField
                            label="Title"
                            value={selectedAnnotation.title}
                            onChange={(e) => {
                              const updatedAnnotations = imageAnnotations.map(a => 
                                a.id === selectedAnnotation.id 
                                  ? { ...a, title: e.target.value } 
                                  : a
                              );
                              setImageAnnotations(updatedAnnotations);
                              setSelectedAnnotation({ ...selectedAnnotation, title: e.target.value });
                            }}
                            fullWidth
                            size="small"
                          />
                          
                          <TextField
                            label="Description"
                            value={selectedAnnotation.description || ''}
                            onChange={(e) => {
                              const updatedAnnotations = imageAnnotations.map(a => 
                                a.id === selectedAnnotation.id 
                                  ? { ...a, description: e.target.value } 
                                  : a
                              );
                              setImageAnnotations(updatedAnnotations);
                              setSelectedAnnotation({ ...selectedAnnotation, description: e.target.value });
                            }}
                            multiline
                            rows={3}
                            fullWidth
                            size="small"
                          />
                          
                          <Grid container spacing={1}>
                            <Grid item xs={6}>
                              <TextField
                                label="X Position %"
                                type="number"
                                value={Math.round(selectedAnnotation.position.x)}
                                onChange={(e) => {
                                  const x = Math.max(0, Math.min(100, parseFloat(e.target.value) || 0));
                                  const updatedAnnotations = imageAnnotations.map(a => 
                                    a.id === selectedAnnotation.id 
                                      ? { ...a, position: { ...a.position, x } } 
                                      : a
                                  );
                                  setImageAnnotations(updatedAnnotations);
                                  setSelectedAnnotation({ 
                                    ...selectedAnnotation, 
                                    position: { ...selectedAnnotation.position, x }
                                  });
                                }}
                                fullWidth
                                size="small"
                                inputProps={{ min: 0, max: 100 }}
                              />
                            </Grid>
                            <Grid item xs={6}>
                              <TextField
                                label="Y Position %"
                                type="number"
                                value={Math.round(selectedAnnotation.position.y)}
                                onChange={(e) => {
                                  const y = Math.max(0, Math.min(100, parseFloat(e.target.value) || 0));
                                  const updatedAnnotations = imageAnnotations.map(a => 
                                    a.id === selectedAnnotation.id 
                                      ? { ...a, position: { ...a.position, y } } 
                                      : a
                                  );
                                  setImageAnnotations(updatedAnnotations);
                                  setSelectedAnnotation({ 
                                    ...selectedAnnotation, 
                                    position: { ...selectedAnnotation.position, y }
                                  });
                                }}
                                fullWidth
                                size="small"
                                inputProps={{ min: 0, max: 100 }}
                              />
                            </Grid>
                          </Grid>
                          
                          <FormControl fullWidth size="small">
                            <InputLabel>Color</InputLabel>
                            <Select
                              value={selectedAnnotation.style?.color || 'primary'}
                              label="Color"
                              onChange={(e) => {
                                const updatedAnnotations = imageAnnotations.map(a => 
                                  a.id === selectedAnnotation.id 
                                    ? { ...a, style: { ...a.style, color: e.target.value } } 
                                    : a
                                );
                                setImageAnnotations(updatedAnnotations);
                                setSelectedAnnotation({ 
                                  ...selectedAnnotation, 
                                  style: { ...selectedAnnotation.style, color: e.target.value }
                                });
                              }}
                            >
                              <MenuItem value="primary">Blue</MenuItem>
                              <MenuItem value="secondary">Purple</MenuItem>
                              <MenuItem value="error">Red</MenuItem>
                              <MenuItem value="warning">Orange</MenuItem>
                              <MenuItem value="success">Green</MenuItem>
                            </Select>
                          </FormControl>
                        </Box>
                      </CardContent>
                    </Card>
                  )}
                </Box>
              </TabPanel>
              
              {/* Tab 3 & 4: Actions and Tutorial - Similar to create version */}
              {/* <TabPanel value={tabIndex} index={2}>
                <Typography variant="body2" color="text.secondary" align="center" sx={{ p: 3 }}>
                  Actions management for image step editing
                  <br />
                  (Implementation similar to create version)
                </Typography>
              </TabPanel>
              
              <TabPanel value={tabIndex} index={3}>
                <Typography variant="body2" color="text.secondary" align="center" sx={{ p: 3 }}>
                  Interactive tutorial management
                  <br />
                  (Implementation similar to create version)
                </Typography>
              </TabPanel> */}
            </Box>
          </Paper>
        </Box>
      </Box>

      {/* Update Progress */}
      {updating && (
        <Box sx={{ 
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 9999,
          background: 'rgba(255, 255, 255, 0.95)',
          padding: 3,
          borderRadius: 2,
          minWidth: '300px',
          textAlign: 'center',
          boxShadow: '0 4px 20px rgba(0,0,0,0.3)'
        }}>
          <Typography variant="h6" gutterBottom>
            Updating Image Topic...
          </Typography>
          <Typography variant="body2" gutterBottom sx={{ mb: 2 }}>
            {imageFile ? `Uploading new image: ${imageFile.name}...` : 'Saving topic changes...'}
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={updateProgress} 
            sx={{ mb: 2, height: 8, borderRadius: 4 }}
          />
          <Typography variant="body2" color="text.secondary">
            {updateProgress}% complete
          </Typography>
        </Box>
      )}

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default EditImageStep;