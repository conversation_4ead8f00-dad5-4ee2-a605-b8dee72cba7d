import React, { useState, useEffect, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  IconButton,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  // NEW: Import for multiple selection
  OutlinedInput,
  Checkbox,
  ListItemText,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  InsertDriveFile as FileIcon,
  PictureAsPdf as PdfIcon,
  Image as ImageIcon,
  VideoLibrary as VideoIcon,
  Description as DocIcon,
  Archive as ArchiveIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import adminFileService from '../services/adminFileService';

function FileManagement() {
  const [files, setFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});
  const [isUploading, setIsUploading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileCourse, setFileCourse] = useState('manual');
  const [fileDescription, setFileDescription] = useState('');
  // UPDATED: Change from single targetRole to multiple targetRoles
  const [targetRoles, setTargetRoles] = useState(['installer']);
  const [selectedRole, setSelectedRole] = useState('installer');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  // NEW: State for delete confirmation dialog
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [fileToDelete, setFileToDelete] = useState(null);

  const roles = [
    { value: 'installer', label: 'Installer' },
    { value: 'engineer', label: 'Engineer' },
    { value: 'architect', label: 'Architect' },
    { value: 'salesperson', label: 'Salesperson' },
  ];

  const loadFiles = useCallback(async () => {
    try {
      setLoading(true);
      const response = await adminFileService.getFilesByRole(selectedRole);
      setFiles(response.files || []);
    } catch (error) {
      console.error('Error loading files:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load files. Please try again.',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  }, [selectedRole]);

  useEffect(() => {
    loadFiles();
  }, [loadFiles]);

  const onDrop = useCallback((acceptedFiles) => {
    acceptedFiles.forEach((file) => {
      setSelectedFile(file);
      setOpenDialog(true);
    });
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    // REMOVED FILE TYPE RESTRICTIONS - Now accepts all file types
    // Original restrictions (commented for backup):
    // accept: {
    //   'application/pdf': ['.pdf'],
    //   'image/*': ['.png', '.jpg', '.jpeg', '.gif'],
    //   'video/*': ['.mp4', '.avi', '.mov'],
    //   'application/msword': ['.doc'],
    //   'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    //   'application/zip': ['.zip', '.rar'],
    // },
  });

  // UPDATED: Handle upload with multiple roles
  const handleUpload = async () => {
    if (!selectedFile || targetRoles.length === 0) return;

    setIsUploading(true);
    setOpenDialog(false);

    const fileId = Date.now();
    setUploadProgress({ [fileId]: 0 });

    try {
      const metadata = {
        course: fileCourse,
        description: fileDescription,
        onProgress: (progress) => {
          setUploadProgress(prev => ({ ...prev, [fileId]: progress }));
        }
      };

      // Use new multiple roles upload method
      await adminFileService.uploadFileToMultipleRoles(selectedFile, targetRoles, metadata);

      // Clear progress and reload files
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[fileId];
        return newProgress;
      });

      setSnackbar({
        open: true,
        message: `File uploaded successfully to ${targetRoles.length} role(s): ${targetRoles.join(', ')}!`,
        severity: 'success'
      });

      // Reload files if we're viewing any of the uploaded roles
      if (targetRoles.includes(selectedRole)) {
        loadFiles();
      }

      setSelectedFile(null);
      setFileDescription('');
      setFileCourse('manual');
      setTargetRoles(['installer']); // Reset to default
    } catch (error) {
      console.error('Upload error:', error);
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[fileId];
        return newProgress;
      });
      setSnackbar({
        open: true,
        message: 'Failed to upload file. Please try again.',
        severity: 'error'
      });
    } finally {
      setIsUploading(false);
    }
  };

  // NEW: Open delete confirmation dialog
  const handleOpenDeleteDialog = (file) => {
    setFileToDelete(file);
    setOpenDeleteDialog(true);
  };

  // NEW: Close delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setFileToDelete(null);
  };

  // UPDATED: Confirm and execute delete
  const handleConfirmDeleteFile = async () => {
    if (!fileToDelete) return;
    
    try {
      await adminFileService.deleteFile(selectedRole, fileToDelete.name);
      setSnackbar({
        open: true,
        message: 'File deleted successfully!',
        severity: 'info'
      });
      handleCloseDeleteDialog();
      loadFiles(); // Reload files
    } catch (error) {
      console.error('Delete error:', error);
      setSnackbar({
        open: true,
        message: 'Failed to delete file. Please try again.',
        severity: 'error'
      });
    }
  };

  // LEGACY: Old direct delete function (commented out)
  // const handleDeleteFile = async (fileName) => {
  //   try {
  //     await adminFileService.deleteFile(selectedRole, fileName);
  //     setSnackbar({
  //       open: true,
  //       message: 'File deleted successfully!',
  //       severity: 'info'
  //     });
  //     loadFiles(); // Reload files
  //   } catch (error) {
  //     console.error('Delete error:', error);
  //     setSnackbar({
  //       open: true,
  //       message: 'Failed to delete file. Please try again.',
  //       severity: 'error'
  //     });
  //   }
  // };

  const handlePreviewFile = (file) => {
    try {
      // Use previewUrl directly from backend response
      window.open(file.previewUrl || file.url, '_blank', 'noopener,noreferrer');
      
      setSnackbar({
        open: true,
        message: 'Opening file preview...',
        severity: 'info'
      });
    } catch (error) {
      console.error('Preview error:', error);
      setSnackbar({
        open: true,
        message: 'Failed to preview file. Please try again.',
        severity: 'error'
      });
    }
  };

  const handleDownloadFile = (file) => {
    try {
      // Use downloadUrl directly from backend response
      const link = document.createElement('a');
      link.href = file.downloadUrl || file.url;
      link.download = file.name;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setSnackbar({
        open: true,
        message: 'File download started...',
        severity: 'success'
      });
    } catch (error) {
      console.error('Download error:', error);
      setSnackbar({
        open: true,
        message: 'Failed to download file. Please try again.',
        severity: 'error'
      });
    }
  };

  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (['pdf'].includes(extension)) return <PdfIcon />;
    if (['png', 'jpg', 'jpeg', 'gif'].includes(extension)) return <ImageIcon />;
    if (['mp4', 'avi', 'mov'].includes(extension)) return <VideoIcon />;
    if (['doc', 'docx'].includes(extension)) return <DocIcon />;
    if (['zip', 'rar'].includes(extension)) return <ArchiveIcon />;
    return <FileIcon />;
  };

  // Remove unused function
  // const getCourseColor = (course) => {
  //   switch (course) {
  //     case 'manual': return 'primary';
  //     case 'diagram': return 'success';
  //     case 'video': return 'warning';
  //     case 'software': return 'info';
  //     default: return 'default';
  //   }
  // };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          File Management
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={loadFiles}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {/* Role Selector */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Select Role Folder to View
        </Typography>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Role</InputLabel>
          <Select
            value={selectedRole}
            label="Role"
            onChange={(e) => setSelectedRole(e.target.value)}
          >
            {roles.map((role) => (
              <MenuItem key={role.value} value={role.value}>
                {role.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Paper>

      {/* Upload Area */}
      <Paper
        {...getRootProps()}
        sx={{
          p: 4,
          mb: 4,
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          backgroundColor: isDragActive ? 'primary.light' : 'grey.50',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            borderColor: 'primary.main',
            backgroundColor: 'primary.light',
          },
        }}
      >
        <input {...getInputProps()} />
        <Box sx={{ textAlign: 'center' }}>
          <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            {isDragActive ? 'Drop files here...' : 'Drag & drop files here, or click to select'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Admin can upload files to any role folder
          </Typography>
          <Typography variant="body2" color="text.secondary">
            All file types are now supported (no restrictions)
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            sx={{ mt: 2 }}
          >
            Select Files
          </Button>
        </Box>
      </Paper>

      {/* Upload Progress */}
      {Object.keys(uploadProgress).length > 0 && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Uploading...
          </Typography>
          {Object.entries(uploadProgress).map(([fileId, progress]) => (
            <Box key={fileId} sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" sx={{ flex: 1 }}>
                  {selectedFile?.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {progress}%
                </Typography>
              </Box>
              <LinearProgress variant="determinate" value={progress} />
            </Box>
          ))}
        </Paper>
      )}

      {/* Files List */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
          Files in {selectedRole.charAt(0).toUpperCase() + selectedRole.slice(1)} Folder ({files.length})
        </Typography>
        
        {loading ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <LinearProgress />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Loading files...
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={2}>
            {files.map((file) => (
              <Grid item xs={12} sm={6} md={4} key={file.key}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ color: 'primary.main', mr: 2 }}>
                        {getFileIcon(file.name)}
                      </Box>
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography variant="body1" noWrap sx={{ fontWeight: 'medium' }}>
                          {file.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatFileSize(file.size)} • {new Date(file.lastModified).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </Box>
                    
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                      <Box>
                        <IconButton 
                          size="small" 
                          color="primary"
                          onClick={() => handlePreviewFile(file)}
                        >
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton 
                          size="small" 
                          color="success"
                          onClick={() => handleDownloadFile(file)}
                        >
                          <DownloadIcon />
                        </IconButton>
                      </Box>
                      <IconButton 
                        size="small" 
                        color="error"
                        onClick={() => handleOpenDeleteDialog(file)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {files.length === 0 && !loading && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <FileIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
            <Typography variant="h6" color="text.secondary">
              No files in {selectedRole} folder yet
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Upload files using the drag & drop area above
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Upload Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Upload File Details</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body1" gutterBottom>
              <strong>File:</strong> {selectedFile?.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Size: {selectedFile ? formatFileSize(selectedFile.size) : ''}
            </Typography>
            
            <FormControl fullWidth margin="normal">
              <InputLabel>Target Role Folder</InputLabel>
              <Select
                multiple
                value={targetRoles}
                label="Target Role Folder"
                onChange={(e) => setTargetRoles(e.target.value)}
                input={<OutlinedInput label="Target Role Folder" />}
                renderValue={(selected) => selected.map((value) => roles.find((role) => role.value === value)?.label).join(', ')}
              >
                {roles.map((role) => (
                  <MenuItem key={role.value} value={role.value}>
                    <Checkbox checked={targetRoles.indexOf(role.value) > -1} />
                    <ListItemText primary={role.label} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            {/* <TextField
              select
              label="Course"
              value={fileCourse}
              onChange={(e) => setFileCourse(e.target.value)}
              fullWidth
              margin="normal"
            >
              <MenuItem value="manual">Manual</MenuItem>
              <MenuItem value="diagram">Diagram</MenuItem>
              <MenuItem value="video">Video</MenuItem>
              <MenuItem value="software">Software</MenuItem>
              <MenuItem value="general">General</MenuItem>
            </TextField> */}
            
            <TextField
              label="Description"
              value={fileDescription}
              onChange={(e) => setFileDescription(e.target.value)}
              fullWidth
              multiline
              rows={3}
              margin="normal"
              placeholder="Enter file description..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleUpload} 
            variant="contained" 
            disabled={isUploading}
          >
            Upload to {targetRoles.length} Role(s) Folder
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-confirmation-dialog-title"
        aria-describedby="delete-confirmation-dialog-description"
      >
        <DialogTitle id="delete-confirmation-dialog-title">
          Confirm Delete
        </DialogTitle>
        <DialogContent>
          <Typography id="delete-confirmation-dialog-description">
            Are you sure you want to delete the file "<strong>{fileToDelete?.name}</strong>" from the {selectedRole} folder?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="primary">
            Cancel
          </Button>
          <Button 
            onClick={handleConfirmDeleteFile} 
            color="secondary" 
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          severity={snackbar.severity} 
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default FileManagement;