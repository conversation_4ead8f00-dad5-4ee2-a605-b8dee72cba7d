import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL;

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// API service for admin panel
export const adminFileService = {
  /**
   * Upload file to specific role folder
   * @param {File} file - File to upload
   * @param {string} targetRole - Target role folder
   * @param {Object} metadata - File metadata
   * @returns {Promise} Upload result
   */
  uploadFile: async (file, targetRole, metadata = {}) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('course', metadata.course || 'general');
    formData.append('description', metadata.description || '');
    formData.append('targetRole', targetRole);

    const response = await api.post(`/files/role/admin/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (metadata.onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          metadata.onProgress(percentCompleted);
        }
      },
    });

    return response.data;
  },

  /**
   * Upload file to multiple role folders
   * @param {File} file - File to upload
   * @param {Array<string>} targetRoles - Array of target role folders
   * @param {Object} metadata - File metadata
   * @returns {Promise} Upload result
   */
  uploadFileToMultipleRoles: async (file, targetRoles, metadata = {}) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('course', metadata.course || 'general');
    formData.append('description', metadata.description || '');
    formData.append('targetRoles', JSON.stringify(targetRoles));

    const response = await api.post(`/files/upload-to-multiple-roles`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (metadata.onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          metadata.onProgress(percentCompleted);
        }
      },
    });

    return response.data;
  },

  /**
   * Get files by role
   * @param {string} role - Role to get files for
   * @returns {Promise} Files list
   */
  getFilesByRole: async (role) => {
    const response = await api.get(`/files/role/${role}`);
    return response.data;
  },

  /**
   * Delete file
   * @param {string} role - Role folder
   * @param {string} fileName - File name to delete
   * @returns {Promise} Delete result
   */
  deleteFile: async (role, fileName) => {
    const response = await api.delete(`/files/role/${role}/file/${encodeURIComponent(fileName)}`);
    return response.data;
  },

  /**
   * Get file URL with specific action
   * @param {string} role - Role folder
   * @param {string} fileName - File name
   * @param {string} action - Action type: 'preview' or 'download'
   * @param {number} expires - URL expiration in seconds
   * @returns {Promise} File URL data
   */
  getFileUrlWithAction: async (role, fileName, action = 'preview', expires = 3600) => {
    const response = await api.get(`/files/role/${role}/file/${encodeURIComponent(fileName)}/url`, {
      params: { expires, action }
    });
    return response.data;
  },

  /**
   * Get file URL for preview
   * @param {string} role - Role folder
   * @param {string} fileName - File name
   * @param {number} expires - URL expiration in seconds
   * @returns {Promise} File URL data for preview
   */
  getFileUrlForPreview: async (role, fileName, expires = 3600) => {
    const response = await api.get(`/files/role/${role}/file/${encodeURIComponent(fileName)}/url`, {
      params: { expires, action: 'preview' }
    });
    return response.data;
  },

  /**
   * Get file URL for download
   * @param {string} role - Role folder
   * @param {string} fileName - File name
   * @param {number} expires - URL expiration in seconds
   * @returns {Promise} File URL data for download
   */
  getFileUrlForDownload: async (role, fileName, expires = 3600) => {
    const response = await api.get(`/files/role/${role}/file/${encodeURIComponent(fileName)}/url`, {
      params: { expires, action: 'download' }
    });
    return response.data;
  },

  /**
   * Get file URL
   * @param {string} role - Role folder
   * @param {string} fileName - File name
   * @param {number} expires - URL expiration in seconds
   * @returns {Promise} File URL data
   */
  getFileUrl: async (role, fileName, expires = 3600) => {
    const response = await api.get(`/files/role/${role}/file/${encodeURIComponent(fileName)}/url`, {
      params: { expires }
    });
    return response.data;
  },
};

export default adminFileService;