import React, { useState, useEffect } from 'react';
import { Html, TransformControls } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import * as THREE from 'three';
import { convertToSignedUrl } from '../utils/wasabiHelper';

// Transform Controls Wrapper Component
function TransformControlsWrapper({
  targetRef,
  mode,
  onChange,
  onMouseDown,
  onMouseUp,
  enabled = true
}) {
  if (!enabled || !targetRef?.current) {
    return null;
  }

  return (
    <TransformControls
      object={targetRef.current}
      mode={mode}
      onChange={onChange}
      onMouseDown={onMouseDown}
      onMouseUp={onMouseUp}
      showX={true}
      showY={true}
      showZ={true}
      size={0.8}
      space="world"
    />
  );
}

// Attached Model Component
const AttachedModel3D = React.forwardRef(({
  attachedModel,
  onSelect
}, ref) => {
  const [loadedModel, setLoadedModel] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    if (attachedModel.modelUrl) {
      setLoading(true);
      setError(null);
      setLoadedModel(null);

      // Check if URL is from Wasabi storage and convert to signed URL
      const isWasabiUrl = (url) => {
        if (!url) return false;
        return url.includes('wasabisys.com') ||
               url.includes('airsmart') ||
               (!url.includes('://') && !url.startsWith('blob:')); // If no protocol and not blob, assume it's a Wasabi path
      };

      // Process URL - create signed URL if it's a Wasabi URL
      let finalUrl = attachedModel.modelUrl;
      if (isWasabiUrl(finalUrl)) {
        finalUrl = convertToSignedUrl(finalUrl);
        console.log('🔄 Converting Wasabi URL to signed URL:');
        console.log('  Original:', attachedModel.modelUrl);
        console.log('  Signed:', finalUrl);
      }

      const gltfLoader = new GLTFLoader();
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
      gltfLoader.setDRACOLoader(dracoLoader);

      gltfLoader.load(
        finalUrl,
        (gltf) => {
          console.log('✅ Attached model loaded successfully:', gltf);

          // Scale model to reasonable size
          const box = new THREE.Box3().setFromObject(gltf.scene);
          const size = box.getSize(new THREE.Vector3()).length();
          const scale = size > 0 ? 0.8 / size : 0.1; // Smaller scale for attached models
          gltf.scene.scale.setScalar(scale);

          // Center the model
          const center = box.getCenter(new THREE.Vector3());
          gltf.scene.position.copy(center).multiplyScalar(-scale);

          setLoadedModel(gltf);
          setLoading(false);
        },
        (progress) => {
          if (progress.lengthComputable) {
            const percentComplete = (progress.loaded / progress.total) * 100;
            console.log('📊 Attached model loading progress:', percentComplete.toFixed(2) + '%');
          }
        },
        (error) => {
          console.error('❌ Error loading attached model:', error);
          setError(error);
          setLoading(false);
        }
      );
    }
  }, [attachedModel.modelUrl]);



  if (loading) {
    return (
      <Html center>
        <div style={{
          background: 'rgba(0,0,255,0.8)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '4px',
          fontSize: '12px',
          textAlign: 'center'
        }}>
          Loading Attached Model...
        </div>
      </Html>
    );
  }

  if (error) {
    return (
      <Html center>
        <div style={{
          background: 'rgba(255,0,0,0.8)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '4px',
          fontSize: '12px',
          textAlign: 'center',
          maxWidth: '200px'
        }}>
          <div>❌ Failed to load attached model</div>
          <div style={{ fontSize: '10px', marginTop: '4px', opacity: 0.8 }}>
            {attachedModel.fileName || 'Unknown file'}
          </div>
        </div>
      </Html>
    );
  }

  if (!loadedModel) {
    return null;
  }

  return (
    <group
      ref={ref}
      position={attachedModel.position || [0, 0, 0]}
      rotation={attachedModel.rotation || [0, 0, 0]}
      onClick={(e) => {
        e.stopPropagation();
        onSelect?.(attachedModel);
      }}
    >
      <primitive
        object={loadedModel.scene.clone()}
      />
    </group>
  );
});

export default AttachedModel3D;
export { TransformControlsWrapper };