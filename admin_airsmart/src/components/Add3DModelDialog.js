import React, { useState, Suspense, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  LinearProgress,
  Paper,
} from '@mui/material';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import Model3DIcon from '@mui/icons-material/ThreeDRotation';
import CloseIcon from '@mui/icons-material/Close';
import adminModuleService from '../services/adminModuleService';
import * as THREE from 'three';

// 3D Model Preview Component - GLTF/GLB Only
function ModelPreview({ modelUrl, onLoadingComplete }) {
  const [loadError, setLoadError] = useState(null);
  const [loadedModel, setLoadedModel] = useState(null);

  React.useEffect(() => {
    let isActive = true;

    const loadModel = async () => {
      try {
        console.log('🔄 Loading GLTF/GLB model:', modelUrl);

        const gltfLoader = new GLTFLoader();

        // Setup DRACO loader for compressed models
        const dracoLoader = new DRACOLoader();
        dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
        gltfLoader.setDRACOLoader(dracoLoader);

        gltfLoader.load(
          modelUrl,
          (gltf) => {
            if (!isActive) return;
            console.log('✅ GLTF model loaded successfully:', gltf);

            // Scale model to fit in view
            const box = new THREE.Box3().setFromObject(gltf.scene);
            const size = box.getSize(new THREE.Vector3()).length();
            const scale = size > 0 ? 2 / size : 1;
            gltf.scene.scale.setScalar(scale);

            // Center the model
            const center = box.getCenter(new THREE.Vector3());
            gltf.scene.position.copy(center).multiplyScalar(-scale);

            setLoadedModel(gltf);
            if (onLoadingComplete) {
              onLoadingComplete(true);
            }
          },
          (progress) => {
            console.log('📊 Loading progress:', (progress.loaded / progress.total * 100) + '%');
          },
          (error) => {
            if (!isActive) return;
            console.error('❌ Error loading GLTF model:', error);
            setLoadError(error);
            if (onLoadingComplete) {
              onLoadingComplete(false);
            }
          }
        );
      } catch (error) {
        if (!isActive) return;
        console.error('❌ Error in model loading process:', error);
        setLoadError(error);
        if (onLoadingComplete) {
          onLoadingComplete(false);
        }
      }
    };

    if (modelUrl) {
      setLoadError(null);
      setLoadedModel(null);
      loadModel();
    }

    return () => {
      isActive = false;
    };
  }, [modelUrl, onLoadingComplete]);

  if (loadError) {
    console.log('🔴 Rendering error cube due to load error');
    return (
      <mesh>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="#ff4444" />
      </mesh>
    );
  }

  if (!loadedModel) {
    return null;
  }

  console.log('🎨 Rendering loaded GLTF model:', loadedModel);

  return (
    <primitive object={loadedModel.scene} />
  );
}

// Loading component for 3D preview
function ModelLoader() {
  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column',
      alignItems: 'center', 
      justifyContent: 'center',
      height: '100%',
      gap: 2
    }}>
      <CircularProgress size={40} thickness={4} />
      <Typography variant="body2" color="text.secondary">
        Loading 3D Model...
      </Typography>
    </Box>
  );
}

function Add3DModelDialog({ open, onClose, moduleData, onModelAdded }) {
  const [modelFile, setModelFile] = useState(null);
  const [modelPreviewUrl, setModelPreviewUrl] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [canvasKey, setCanvasKey] = useState(0);
  const [modelType, setModelType] = useState('auto');

  const fileInputRef = useRef(null);

  // Handle file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const validExtensions = ['.gltf', '.glb'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

    if (!validExtensions.includes(fileExtension)) {
      setError('Please select a valid 3D model file (.gltf, .glb)');
      return;
    }

    // Clear previous error and reset loading state
    setError(null);
    setModelLoaded(false);

    // Revoke previous URL if exists
    if (modelPreviewUrl) {
      URL.revokeObjectURL(modelPreviewUrl);
    }

    // Set file and create preview URL
    setModelFile(file);
    const previewUrl = URL.createObjectURL(file);
    setModelPreviewUrl(previewUrl);

    console.log('📁 3D Model file selected:', {
      name: file.name,
      size: (file.size / 1024 / 1024).toFixed(2) + ' MB',
      type: file.type,
      extension: fileExtension,
      previewUrl: previewUrl
    });

    // Auto-detect model type based on file extension
    if (modelType === 'auto') {
      if (fileExtension === '.gltf' || fileExtension === '.glb') {
        setModelType('gltf');
      } else {
        setModelType('obj');
      }
    }

    // Force canvas re-render
    setCanvasKey(prevKey => prevKey + 1);
  };

  // Handle model loading completion
  const handleModelLoadingComplete = (success) => {
    setModelLoaded(success);
    if (!success) {
      setError('Failed to load 3D model. Please try a different file.');
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setUploading(true);
      setError(null);
      setUploadProgress(0);

      if (!modelFile) {
        throw new Error('Please select a 3D model file');
      }

      console.log('🔄 Adding 3D model to module...');

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('model', modelFile);
      formData.append('modelType', modelType);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      // Upload 3D model to existing module
      const result = await adminModuleService.add3DModelToModule(moduleData.id, formData);
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      console.log('✅ 3D model added successfully:', result);

      // Notify parent component
      if (onModelAdded) {
        onModelAdded(result);
      }

      // Reset form and close
      handleClose();

    } catch (error) {
      console.error('❌ Error adding 3D model:', error);
      setError(error.message || 'Failed to add 3D model');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    // Clean up preview URL
    if (modelPreviewUrl) {
      URL.revokeObjectURL(modelPreviewUrl);
    }
    
    // Reset form
    setModelFile(null);
    setModelPreviewUrl(null);
    setError(null);
    setUploading(false);
    setUploadProgress(0);
    setModelLoaded(false);
    setModelType('auto');
    
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh',
          maxWidth: '600px',
          margin: '0 auto'
        }
      }}
    >
      <DialogTitle 
        sx={{ 
          borderBottom: '1px solid', 
          borderColor: 'divider',
          p: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Model3DIcon color="primary" />
          <Box>
            <Typography variant="h6" component="div">
              Add 3D Model
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {moduleData?.name}
            </Typography>
          </Box>
        </Box>
        <Button
          onClick={handleClose}
          size="small"
          disabled={uploading}
          sx={{ minWidth: 'auto', p: 1 }}
        >
          <CloseIcon />
        </Button>
      </DialogTitle>
      
      <DialogContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* Module Info */}
          <Paper sx={{ p: 2, bgcolor: 'background.default', border: '1px solid', borderColor: 'divider' }}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
              Module Information
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              <strong>Name:</strong> {moduleData?.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Description:</strong> {moduleData?.description}
            </Typography>
          </Paper>

          {/* Model Type Selection */}
          <FormControl fullWidth disabled={uploading} variant="outlined" size="small">
            <InputLabel>Model Type</InputLabel>
            <Select
              value={modelType}
              onChange={(e) => setModelType(e.target.value)}
              label="Model Type"
            >
              <MenuItem value="auto">Auto Detect</MenuItem>
              <MenuItem value="gltf">GLTF/GLB</MenuItem>
            </Select>
          </FormControl>

          {/* 3D Model Upload Area */}
          <Box 
            sx={{ 
              display: 'flex',
              flexDirection: 'column',
              border: '2px dashed',
              borderColor: 'divider',
              borderRadius: 2,
              overflow: 'hidden',
              minHeight: '300px',
              position: 'relative'
            }}
          >
            {!modelPreviewUrl ? (
              <Box 
                onClick={() => fileInputRef.current?.click()}
                sx={{
                  flex: 1,
                  display: 'flex', 
                  flexDirection: 'column',
                  alignItems: 'center', 
                  justifyContent: 'center',
                  gap: 2,
                  cursor: 'pointer',
                  p: 4,
                  minHeight: '300px',
                  '&:hover': {
                    backgroundColor: uploading ? 'transparent' : 'action.hover',
                    borderColor: uploading ? 'divider' : 'primary.main',
                  }
                }}
              >
                <CloudUploadIcon sx={{ fontSize: 60, color: 'text.secondary' }} />
                <Typography variant="h6" color="text.primary" align="center">
                  Upload 3D Model
                </Typography>
                <Typography variant="body2" color="text.secondary" align="center">
                  Click here or drag and drop your 3D model file
                </Typography>
                <Typography variant="caption" color="text.secondary" align="center">
                  Supported formats: GLTF, GLB
                </Typography>
                <Button 
                  variant="contained" 
                  disabled={uploading}
                  sx={{ mt: 2 }}
                  size="large"
                >
                  Select File
                </Button>
              </Box>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', height: '300px' }}>
                <Box sx={{ flex: 1, position: 'relative' }}>
                  {/* Canvas with key prop for forcing re-render */}
                  <Canvas
                    key={canvasKey}
                    camera={{ position: [3, 3, 5], fov: 50 }}
                    style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
                    frameloop="demand"
                    gl={{ antialias: true, alpha: true }}
                    onCreated={({ gl }) => {
                      gl.setClearColor('#f5f5f5', 1);
                    }}
                  >
                    {/* Enhanced lighting setup for better visibility */}
                    <ambientLight intensity={0.6} />
                    <directionalLight position={[10, 10, 5]} intensity={1.2} castShadow />
                    <directionalLight position={[-10, -10, -5]} intensity={0.8} />
                    <directionalLight position={[0, 10, 10]} intensity={0.8} />
                    <pointLight position={[0, 10, 0]} intensity={0.7} />
                    <pointLight position={[5, 0, 5]} intensity={0.5} />
                    <pointLight position={[-5, 0, -5]} intensity={0.5} />

                    <Suspense fallback={<primitive object={new THREE.Object3D()} />}>
                      <ModelPreview
                        modelUrl={modelPreviewUrl}
                        onLoadingComplete={handleModelLoadingComplete}
                      />
                    </Suspense>
                    <OrbitControls
                      makeDefault
                      enablePan={true}
                      enableZoom={true}
                      enableRotate={true}
                      enableDamping={true}
                      dampingFactor={0.05}
                      rotateSpeed={1.0}
                      zoomSpeed={1.5}
                      panSpeed={0.8}
                      autoRotate={false}
                    />
                  </Canvas>
                  
                  {/* Loading overlay */}
                  {modelPreviewUrl && !modelLoaded && (
                    <Box 
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        zIndex: 10
                      }}
                    >
                      <ModelLoader />
                    </Box>
                  )}
                </Box>

                <Box 
                  sx={{
                    p: 2,
                    borderTop: '1px solid',
                    borderColor: 'divider',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    bgcolor: 'background.paper'
                  }}
                >
                  <Box sx={{ overflow: 'hidden' }}>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontWeight: 500,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        maxWidth: '200px'
                      }}
                    >
                      {modelFile?.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {(modelFile?.size / 1024 / 1024).toFixed(2)} MB
                    </Typography>
                  </Box>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploading}
                  >
                    Change File
                  </Button>
                </Box>
              </Box>
            )}

            <input
              ref={fileInputRef}
              type="file"
              accept=".gltf,.glb"
              style={{ display: 'none' }}
              onChange={handleFileSelect}
              disabled={uploading}
            />
          </Box>

          {/* Upload Progress */}
          {uploading && (
            <Box>
              <Typography variant="body2" gutterBottom sx={{ textAlign: 'center' }}>
                Uploading 3D model... {uploadProgress}%
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={uploadProgress} 
                sx={{ borderRadius: 1 }}
              />
            </Box>
          )}

          {/* Error Display */}
          {error && (
            <Alert 
              severity="error" 
              sx={{ borderRadius: 1 }}
            >
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions 
        sx={{ 
          p: 3, 
          borderTop: '1px solid', 
          borderColor: 'divider',
          display: 'flex',
          justifyContent: 'space-between'
        }}
      >
        <Button 
          onClick={handleClose} 
          disabled={uploading}
          variant="outlined"
          sx={{ minWidth: '100px' }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          disabled={uploading || !modelFile}
          variant="contained"
          sx={{ minWidth: '140px' }}
        >
          {uploading ? 'Uploading...' : 'Add 3D Model'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default Add3DModelDialog;