import React, { useState, useRef, useEffect, Suspense } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Fab,
  Tooltip,
  <PERSON>nackbar,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Upload as UploadIcon,
  ThreeDRotation as Model3DIcon,
} from '@mui/icons-material';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Html } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import adminModuleService from '../services/adminModuleService';
import * as THREE from 'three';

import { convertToSignedUrl } from '../utils/wasabiHelper'; // Import wasabiHelper

// 3D Model Component - GLTF/GLB Only
function Model({ modelUrl, onModelClick }) {
  const meshRef = useRef();
  const [loadedModel, setLoadedModel] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    if (modelUrl) {
      setLoading(true);
      setError(null);
      setLoadedModel(null);
      
      const gltfLoader = new GLTFLoader();

      // Setup DRACO loader for compressed models
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
      gltfLoader.setDRACOLoader(dracoLoader);

      gltfLoader.load(
        modelUrl,
        // onLoad
        (gltf) => {
          console.log('✅ GLTF model loaded successfully:', gltf);

          // Scale model to fit in view
          const box = new THREE.Box3().setFromObject(gltf.scene);
          const size = box.getSize(new THREE.Vector3()).length();
          const scale = size > 0 ? 2 / size : 1;
          gltf.scene.scale.setScalar(scale);

          // Center the model
          const center = box.getCenter(new THREE.Vector3());
          gltf.scene.position.copy(center).multiplyScalar(-scale);

          setLoadedModel(gltf);
          setLoading(false);
        },
        // onProgress
        (progress) => {
          if (progress.lengthComputable) {
            const percentComplete = (progress.loaded / progress.total) * 100;
            console.log('📊 GLTF loading progress:', percentComplete.toFixed(2) + '%');
          }
        },
        // onError
        (error) => {
          console.error('❌ Error loading GLTF model:', error);
          setError(error);
          setLoading(false);
        }
      );
    } else {
      setLoadedModel(null);
      setLoading(false);
      setError(null);
    }
  }, [modelUrl]);
  
  useFrame(() => {
    if (meshRef.current) {
      // Any animations can go here
    }
  });

  // Show loading state
  if (loading) {
    return (
      <Html center>
        <div style={{
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '12px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          textAlign: 'center'
        }}>
          Loading GLTF Model...
        </div>
      </Html>
    );
  }

  // Show error state
  if (error) {
    return (
      <Html center>
        <div style={{
          background: 'rgba(255,0,0,0.8)',
          color: 'white',
          padding: '12px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          textAlign: 'center',
          maxWidth: '200px'
        }}>
          <div>❌ Failed to load GLTF model</div>
          <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}>
            Check URL and CORS settings
          </div>
        </div>
      </Html>
    );
  }

  // Only render if we have a valid model
  if (!loadedModel) {
    return null;
  }

  return (
    <primitive 
      ref={meshRef}
      object={loadedModel.scene} 
      scale={[1, 1, 1]}
      onClick={onModelClick}
    />
  );
}

// Fallback component for when no model is loaded
function DefaultScene() {
  return (
    <mesh>
      <boxGeometry args={[2, 2, 2]} />
      <meshStandardMaterial color="#cccccc" />
    </mesh>
  );
}

// 3D Model Viewer Component
function ModelViewer({ 
  modelUrl, 
  hotspots = [], 
  onAddHotspot, 
  onUpdateCamera, 
  cameraPosition,
  selectedHotspot,
  onSelectHotspot 
}) {
  const controlsRef = useRef();

  const handleModelClick = (event) => {
    if (!onAddHotspot) return;
    
    // Convert screen coordinates to 3D world coordinates
    const rect = event.target.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    const y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    
    // This is simplified - in reality you'd need proper raycasting
    const position = [x * 5, y * 5, 0]; // Mock position
    
    onAddHotspot(position);
  };

  const handleCameraChange = () => {
    if (controlsRef.current && onUpdateCamera) {
      const camera = controlsRef.current.object;
      onUpdateCamera({
        position: [camera.position.x, camera.position.y, camera.position.z],
        target: [
          controlsRef.current.target.x,
          controlsRef.current.target.y,
          controlsRef.current.target.z
        ]
      });
    }
  };

  return (
    <>
      <OrbitControls 
        ref={controlsRef}
        onChange={handleCameraChange}
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
      />
      
      {/* 3D Model or Default Scene */}
      <Suspense fallback={null}>
        {modelUrl ? (
          <Model modelUrl={modelUrl} onModelClick={handleModelClick} />
        ) : (
          <DefaultScene />
        )}
      </Suspense>
      
      {/* Hotspots */}
      {hotspots.map((hotspot, index) => (
        <mesh 
          key={hotspot.id || index}
          position={hotspot.position}
          onClick={() => onSelectHotspot?.(hotspot)}
        >
          <sphereGeometry args={[0.1, 16, 16]} />
          <meshBasicMaterial 
            color={selectedHotspot?.id === hotspot.id ? '#ff4444' : '#44ff44'} 
          />
          <Html distanceFactor={10}>
            <div 
              style={{
                background: 'rgba(0,0,0,0.8)',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '4px',
                fontSize: '12px',
                pointerEvents: 'none'
              }}
            >
              {hotspot.label}
            </div>
          </Html>
        </mesh>
      ))}
      
      {/* Enhanced default lighting for better visibility */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={1.2} castShadow />
      <directionalLight position={[-10, -10, -5]} intensity={0.8} />
      <directionalLight position={[0, 10, 10]} intensity={0.8} />
      <pointLight position={[0, 10, 0]} intensity={0.7} />
      <pointLight position={[5, 0, 5]} intensity={0.5} />
      <pointLight position={[-5, 0, -5]} intensity={0.5} />
    </>
  );
}

// Loading component for 3D canvas
function CanvasLoader() {
  return (
    <Box sx={{ 
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 2
    }}>
      <CircularProgress />
      <Typography variant="body2" color="text.secondary">
        Loading 3D Model...
      </Typography>
    </Box>
  );
}

// Main Visual 3D Module Builder Component
function Visual3DModuleBuilder({ moduleId, moduleName, onSave, onCancel }) {
  // 3D Editor State
  const [modelUrl, setModelUrl] = useState('');
  const [moduleData, setModuleData] = useState(null);
  const [cameraPosition, setCameraPosition] = useState({
    position: [0, 2, 5],
    target: [0, 0, 0]
  });
  
  const [loading, setLoading] = useState(false);
  
  // Snackbar state
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Load existing module data including model URL
  useEffect(() => {
    const loadModuleData = async () => {
      setLoading(true);
      try {
        console.log('🔄 Loading module data for:', moduleId);
        
        // Load module data to get the 3D model URL
        const module = await adminModuleService.getModule(moduleId);
        console.log('✅ Module data loaded:', module);
        
        // Set module data and model URL
        setModuleData(module);
        if (module.modelUrl) {
          // Convert to signed URL for Wasabi S3 access
          const signedUrl = convertToSignedUrl(module.modelUrl);
          console.log('✅ Original model URL:', module.modelUrl);
          console.log('✅ Signed model URL:', signedUrl);
          setModelUrl(signedUrl);
        } else {
          console.warn('⚠️ Module has no 3D model URL');
          setSnackbar({
            open: true,
            message: 'This module does not have a 3D model. Please upload one.',
            severity: 'warning'
          });
        }
        
      } catch (error) {
        console.error('❌ Error loading module data:', error);
        setSnackbar({
          open: true,
          message: `Error loading module data: ${error.message}`,
          severity: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    if (moduleId) {
      loadModuleData();
    }
  }, [moduleId]);

  const handleUpdateCamera = (newCamera) => {
    setCameraPosition(newCamera);
  };

  return (
    <Box sx={{ height: '90vh', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {/* Header - Compact height */}
      <Paper sx={{ p: 1.5, mb: 1, flexShrink: 0 }} elevation={2}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontSize: '1.1rem', fontWeight: 600 }}>
            🎯 3D Model Viewer - {moduleName || 'Loading...'}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button onClick={onCancel} disabled={loading} size="small">Close</Button>
          </Box>
        </Box>
      </Paper>

      {/* 3D Viewer - Full height */}
      <Box sx={{ 
        flex: 1,
        minHeight: 0
      }}>
        <Paper sx={{ height: '100%', position: 'relative', overflow: 'hidden' }} elevation={2}>
          {/* Loading state */}
          {loading && !modelUrl && (
            <Box sx={{ 
              height: '100%', 
              display: 'flex', 
              flexDirection: 'column',
              alignItems: 'center', 
              justifyContent: 'center',
              gap: 2
            }}>
              <CircularProgress />
              <Typography variant="h6" color="text.secondary">
                Loading 3D Model...
              </Typography>
            </Box>
          )}
          
          {/* No model URL state */}
          {!loading && !modelUrl && (
            <Box sx={{ 
              height: '100%', 
              display: 'flex', 
              flexDirection: 'column',
              alignItems: 'center', 
              justifyContent: 'center',
              gap: 2
            }}>
              <Model3DIcon sx={{ fontSize: 64, color: 'grey.400' }} />
              <Typography variant="h6" color="text.secondary">
                This module doesn't have a 3D model yet
              </Typography>
              <input
                accept=".obj,.gltf,.glb"
                style={{ display: 'none' }}
                id="model-upload"
                type="file"
                onChange={async (event) => {
                  const file = event.target.files[0];
                  if (file) {
                    // In a real implementation, we would upload this file to storage
                    // and update the module's modelUrl
                    // For now, just create an object URL
                    const mockUrl = URL.createObjectURL(file);
                    setModelUrl(mockUrl);
                    
                    // TODO: In a real implementation, update the module with the new model URL
                    setSnackbar({
                      open: true,
                      message: '3D model loaded from file. In a production environment, this would be uploaded to storage.',
                      severity: 'info'
                    });
                  }
                }}
              />
              <label htmlFor="model-upload">
                <Button
                  variant="contained"
                  component="span"
                  startIcon={<UploadIcon />}
                  size="large"
                >
                  Upload 3D Model
                </Button>
              </label>
              <Typography variant="body2" color="text.secondary">
                Supported formats: OBJ, GLTF, GLB
              </Typography>
            </Box>
          )}

          {/* 3D Canvas */}
          {modelUrl && (
            <Suspense fallback={<CanvasLoader />}>
              <Canvas style={{ height: '100%', width: '100%' }}>
                <ModelViewer
                  modelUrl={modelUrl}
                  hotspots={[]} // No hotspots in view mode
                  onUpdateCamera={handleUpdateCamera}
                  cameraPosition={cameraPosition}
                  selectedHotspot={null}
                  onSelectHotspot={() => {}} // No hotspot interaction
                />
              </Canvas>
            </Suspense>
          )}

          {/* 3D Info Overlay */}
          {modelUrl && (
            <Box sx={{
              position: 'absolute',
              top: 12,
              left: 12,
              display: 'flex',
              gap: 1
            }}>
              <Tooltip title="3D Model Viewer - Read Only">
                <Fab size="small" color="primary" sx={{ width: 40, height: 40 }}>
                  <ViewIcon fontSize="small" />
                </Fab>
              </Tooltip>
            </Box>
          )}
          
          {/* Camera Position Info Overlay - More compact */}
          {modelUrl && (
            <Box sx={{ 
              position: 'absolute', 
              bottom: 12, 
              left: 12, 
              right: 12,
              background: 'rgba(0,0,0,0.75)',
              borderRadius: 1,
              p: 1,
              color: 'white',
              fontSize: '11px'
            }}>
              <Typography variant="caption" sx={{ color: 'white', display: 'block', fontSize: '11px' }}>
                Camera: [{cameraPosition.position.map(p => Number(p).toFixed(1)).join(', ')}]
              </Typography>
              <Typography variant="caption" sx={{ color: 'white', display: 'block', fontSize: '11px' }}>
                Target: [{cameraPosition.target.map(t => Number(t).toFixed(1)).join(', ')}]
              </Typography>
              {moduleData && (
                <Typography variant="caption" sx={{ color: 'white', display: 'block', fontSize: '11px', mt: 0.5 }}>
                  Module: {moduleData.name}
                </Typography>
              )}
            </Box>
          )}
        </Paper>
      </Box>

      {/* Snackbar for feedback */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          severity={snackbar.severity} 
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default Visual3DModuleBuilder;