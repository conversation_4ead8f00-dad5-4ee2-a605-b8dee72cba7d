import React, { useState, Suspense, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Grid,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  LinearProgress,
  Paper,
  Tabs,
  Tab,
  Stack,
  Card,
  CardContent,
  Avatar,
  Divider,
  useTheme,
  alpha,
  Container
} from '@mui/material';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import {
  CloudUpload as CloudUploadIcon,
  ViewInAr as ViewInArIcon,
  Info as InfoIcon,
  ThreeDRotation as Model3DIcon,
  Add as AddIcon,
  Upload as UploadIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import adminModuleService from '../services/adminModuleService';
import * as THREE from 'three';

// 3D Model Preview Component - GLTF/GLB Only
function ModelPreview({ modelUrl, onLoadingComplete }) {
  const [loadError, setLoadError] = useState(null);
  const [loadedModel, setLoadedModel] = useState(null);

  React.useEffect(() => {
    let isActive = true;
    
    const loadModel = async () => {
      try {
        console.log('🔄 Loading GLTF/GLB model:', modelUrl);

        const gltfLoader = new GLTFLoader();

        // Setup DRACO loader for compressed models
        const dracoLoader = new DRACOLoader();
        dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
        gltfLoader.setDRACOLoader(dracoLoader);

        gltfLoader.load(
          modelUrl,
          (gltf) => {
            if (!isActive) return;
            console.log('✅ GLTF model loaded successfully:', gltf);

            // Scale model to fit in view
            const box = new THREE.Box3().setFromObject(gltf.scene);
            const size = box.getSize(new THREE.Vector3()).length();
            const scale = size > 0 ? 2 / size : 1;
            gltf.scene.scale.setScalar(scale);

            // Center the model
            const center = box.getCenter(new THREE.Vector3());
            gltf.scene.position.copy(center).multiplyScalar(-scale);

            setLoadedModel(gltf);
            if (onLoadingComplete) {
              onLoadingComplete(true);
            }
          },
          (progress) => {
            console.log('📊 Loading progress:', (progress.loaded / progress.total * 100) + '%');
          },
          (error) => {
            if (!isActive) return;
            console.error('❌ Error loading GLTF model:', error);
            setLoadError(error);
            if (onLoadingComplete) {
              onLoadingComplete(false);
            }
          }
        );
      } catch (error) {
        if (!isActive) return;
        console.error('❌ Error in model loading process:', error);
        setLoadError(error);
        if (onLoadingComplete) {
          onLoadingComplete(false);
        }
      }
    };
    
    if (modelUrl) {
      setLoadError(null);
      setLoadedModel(null);
      loadModel();
    }
    
    return () => {
      isActive = false;
    };
  }, [modelUrl, onLoadingComplete]);

  if (loadError) {
    console.log('🔴 Rendering error cube due to load error');
    return (
      <mesh>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="#ff4444" />
      </mesh>
    );
  }

  if (!loadedModel) {
    return null;
  }

  console.log('🎨 Rendering loaded GLTF model:', loadedModel);

  return (
    <primitive 
      object={loadedModel.scene} 
      scale={[1, 1, 1]}
      position={[0, 0, 0]}
    />
  );
}

// Enhanced Loading component for 3D preview
function ModelLoader() {
  const theme = useTheme();
  
  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column',
      alignItems: 'center', 
      justifyContent: 'center',
      height: '100%',
      gap: 2,
      p: 3
    }}>
      <CircularProgress 
        size={48} 
        thickness={4} 
        sx={{ color: theme.palette.primary.main }}
      />
      <Typography 
        variant="body1" 
        color="text.secondary"
        fontWeight={500}
      >
        Loading 3D Model...
      </Typography>
      <Typography 
        variant="caption" 
        color="text.secondary"
        align="center"
        sx={{ maxWidth: 200 }}
      >
        Please wait while we process your 3D model
      </Typography>
    </Box>
  );
}

// Enhanced Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`module-tabpanel-${index}`}
      aria-labelledby={`module-tab-${index}`}
      style={{ width: '100%', height: '100%' }}
      {...other}
    >
      {value === index && (
        <Box sx={{ height: '100%' }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `module-tab-${index}`,
    'aria-controls': `module-tabpanel-${index}`,
  };
}

// Enhanced Option Card Component
const OptionCard = ({ title, description, selected, onClick, icon, disabled = false }) => {
  const theme = useTheme();
  
  return (
    <Card
      onClick={!disabled ? onClick : undefined}
      sx={{
        cursor: disabled ? 'not-allowed' : 'pointer',
        border: '2px solid',
        borderColor: selected 
          ? theme.palette.primary.main 
          : 'divider',
        borderRadius: 3,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        opacity: disabled ? 0.6 : 1,
        background: selected 
          ? alpha(theme.palette.primary.main, 0.05)
          : 'background.paper',
        '&:hover': !disabled ? {
          borderColor: theme.palette.primary.main,
          transform: 'translateY(-2px) scale(1.02)',
          boxShadow: `0 12px 24px ${alpha(theme.palette.primary.main, 0.15)}`,
        } : {},
        position: 'relative',
        overflow: 'hidden',
        ...(selected && {
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '3px',
            background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
          }
        })
      }}
    >
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar
            sx={{
              bgcolor: selected 
                ? theme.palette.primary.main 
                : alpha(theme.palette.primary.main, 0.1),
              color: selected 
                ? 'white' 
                : theme.palette.primary.main,
              width: { xs: 40, sm: 48 },
              height: { xs: 40, sm: 48 },
              transition: 'all 0.3s ease'
            }}
          >
            {icon}
          </Avatar>
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography 
              variant="h6" 
              fontWeight={600}
              sx={{ 
                fontSize: { xs: '1rem', sm: '1.25rem' },
                color: selected ? 'primary.main' : 'text.primary',
                mb: 0.5
              }}
            >
              {title}
            </Typography>
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{ 
                lineHeight: 1.5,
                fontSize: { xs: '0.875rem', sm: '1rem' }
              }}
            >
              {description}
            </Typography>
          </Box>
          {selected && (
            <CheckCircleIcon 
              sx={{ 
                color: 'primary.main',
                fontSize: { xs: 20, sm: 24 }
              }} 
            />
          )}
        </Stack>
      </CardContent>
    </Card>
  );
};

function ModuleCreator({ open, onClose, onModuleCreated, courses = [] }) {
  const theme = useTheme();
  
  const [moduleForm, setModuleForm] = useState({
    name: '',
    description: '',
    course: '',
    tags: [],
    modelType: 'auto',
    has3DModel: false
  });

  const [modelFile, setModelFile] = useState(null);
  const [modelPreviewUrl, setModelPreviewUrl] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  const [tagInput, setTagInput] = useState('');
  const [currentTab, setCurrentTab] = useState(0);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [canvasKey, setCanvasKey] = useState(0);

  const fileInputRef = useRef(null);

  // Handle tab change and force canvas re-render when switching to 3D tab
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
    
    if (newValue === 1 && modelPreviewUrl) {
      setCanvasKey(prevKey => prevKey + 1);
    }
  };

  // Handle file selection with improvements
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const validExtensions = ['.gltf', '.glb'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!validExtensions.includes(fileExtension)) {
      setError('Please select a valid 3D model file (.gltf, .glb)');
      return;
    }

    setError(null);
    setModelLoaded(false);
    
    if (modelPreviewUrl) {
      URL.revokeObjectURL(modelPreviewUrl);
    }
    
    setModelFile(file);
    const previewUrl = URL.createObjectURL(file);
    setModelPreviewUrl(previewUrl);
    
    console.log('📁 3D Model file selected:', file.name, 'Size:', (file.size / 1024 / 1024).toFixed(2), 'MB');
    
    if (currentTab !== 1) {
      setCurrentTab(1);
    } else {
      setCanvasKey(prevKey => prevKey + 1);
    }
  };

  // Handle model loading completion
  const handleModelLoadingComplete = (success) => {
    setModelLoaded(success);
    if (!success) {
      setError('Failed to load 3D model. Please try a different file.');
    }
  };

  // Handle tag addition
  const handleAddTag = () => {
    if (tagInput.trim() && !moduleForm.tags.includes(tagInput.trim())) {
      setModuleForm({
        ...moduleForm,
        tags: [...moduleForm.tags, tagInput.trim()]
      });
      setTagInput('');
    }
  };

  // Handle tag removal
  const handleRemoveTag = (tagToRemove) => {
    setModuleForm({
      ...moduleForm,
      tags: moduleForm.tags.filter(tag => tag !== tagToRemove)
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setUploading(true);
      setError(null);
      setUploadProgress(0);

      if (!moduleForm.name.trim()) {
        throw new Error('Module name is required');
      }
      if (!moduleForm.description.trim()) {
        throw new Error('Module description is required');
      }

      console.log('🔄 Creating module...');

      let result;

      if (moduleForm.has3DModel && modelFile) {
        const formData = new FormData();
        formData.append('model', modelFile);
        formData.append('moduleData', JSON.stringify({
          name: moduleForm.name,
          description: moduleForm.description,
          course: moduleForm.course || null,
          tags: moduleForm.tags,
          modelType: moduleForm.modelType,
          has3DModel: true
        }));

        const progressInterval = setInterval(() => {
          setUploadProgress(prev => Math.min(prev + 10, 90));
        }, 200);

        result = await adminModuleService.createModuleWith3DModel(formData);
        clearInterval(progressInterval);
      } else {
        const moduleData = {
          name: moduleForm.name,
          description: moduleForm.description,
          course: moduleForm.course || null,
          tags: moduleForm.tags,
          has3DModel: false
        };

        const progressInterval = setInterval(() => {
          setUploadProgress(prev => Math.min(prev + 15, 90));
        }, 100);

        result = await adminModuleService.createModule(moduleData);
        clearInterval(progressInterval);
      }
      
      setUploadProgress(100);
      console.log('✅ Module created successfully:', result);

      if (onModuleCreated) {
        onModuleCreated(result);
      }

      handleClose();

    } catch (error) {
      console.error('❌ Error creating module:', error);
      setError(error.message || 'Failed to create module');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    if (modelPreviewUrl) {
      URL.revokeObjectURL(modelPreviewUrl);
    }
    
    setModuleForm({
      name: '',
      description: '',
      course: '',
      tags: [],
      modelType: 'auto',
      has3DModel: false
    });
    setModelFile(null);
    setModelPreviewUrl(null);
    setError(null);
    setTagInput('');
    setUploading(false);
    setUploadProgress(0);
    setCurrentTab(0);
    
    onClose();
  };

  // Toggle 3D model inclusion
  const handleToggle3DModel = (include) => {
    setModuleForm({ ...moduleForm, has3DModel: include });
    if (!include) {
      if (modelPreviewUrl) {
        URL.revokeObjectURL(modelPreviewUrl);
      }
      setModelFile(null);
      setModelPreviewUrl(null);
      setModelLoaded(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '90vh',
          maxWidth: '1000px',
          margin: '0 auto',
          // background: 'linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)'
        }
      }}
    >
      <DialogTitle 
        sx={{ 
          borderBottom: '1px solid', 
          borderColor: 'divider',
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
          p: { xs: 2, sm: 3 }
        }}
      >
        <Stack 
          direction={{ xs: 'column', sm: 'row' }} 
          alignItems={{ xs: 'center', sm: 'center' }} 
          spacing={2}
        >
          <Avatar
            sx={{
              bgcolor: theme.palette.primary.main,
              width: { xs: 48, sm: 56 },
              height: { xs: 48, sm: 56 },
            }}
          >
            <ViewInArIcon sx={{ fontSize: { xs: 24, sm: 28 } }} />
          </Avatar>
          <Box sx={{ textAlign: { xs: 'center', sm: 'left' } }}>
            <Typography 
              variant="h4" 
              component="div" 
              fontWeight={700}
              sx={{ 
                fontSize: { xs: '1.5rem', sm: '2rem' },
                background: `linear-gradient(135deg, ${theme.palette.text.primary} 0%, ${theme.palette.primary.main} 100%)`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Create New Module
            </Typography>
            <Typography 
              variant="body1" 
              color="text.secondary"
              sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}
            >
              Build interactive learning experiences with optional 3D models
            </Typography>
          </Box>
        </Stack>
      </DialogTitle>
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Tabs 
          value={currentTab} 
          onChange={handleTabChange} 
          variant="fullWidth"
          indicatorColor="primary"
          textColor="primary"
          aria-label="module creation tabs"
          sx={{
            '& .MuiTab-root': {
              py: { xs: 2, sm: 3 },
              fontSize: { xs: '0.875rem', sm: '1rem' },
              fontWeight: 600
            }
          }}
        >
          <Tab 
            icon={<InfoIcon />} 
            label="Module Details" 
            {...a11yProps(0)}
            disabled={uploading}
          />
          {/* Only show 3D Model tab when 3D Interactive Module is selected */}
          {moduleForm.has3DModel && (
            <Tab 
              icon={<Model3DIcon />} 
              label="3D Model" 
              {...a11yProps(1)}
              disabled={uploading}
            />
          )}
        </Tabs>
      </Box>
      
      <DialogContent sx={{ p: 0, bgcolor: 'background.default' }}>
        <Container maxWidth="md" sx={{ py: { xs: 2, sm: 3 } }}>
          <TabPanel value={currentTab} index={0}>
            <Stack spacing={{ xs: 2, sm: 3 }}>
              {/* Basic Information Section */}
              <Paper 
                elevation={0}
                sx={{ 
                  p: { xs: 2, sm: 3 }, 
                  border: '1px solid', 
                  borderColor: 'divider',
                  borderRadius: 2
                }}
              >
                <Typography 
                  variant="h6" 
                  fontWeight={600} 
                  gutterBottom
                  sx={{ 
                    fontSize: { xs: '1.125rem', sm: '1.25rem' },
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    mb: 2
                  }}
                >
                  <InfoIcon color="primary" />
                  Basic Information
                </Typography>
                
                <Stack spacing={{ xs: 2, sm: 2.5 }}>
                  <TextField
                    label="Module Name"
                    value={moduleForm.name}
                    onChange={(e) => setModuleForm({ ...moduleForm, name: e.target.value })}
                    fullWidth
                    disabled={uploading}
                    variant="outlined"
                    required
                    error={!moduleForm.name && error}
                    helperText="Enter a descriptive name for your module"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2
                      }
                    }}
                  />

                  <TextField
                    label="Description"
                    value={moduleForm.description}
                    onChange={(e) => setModuleForm({ ...moduleForm, description: e.target.value })}
                    multiline
                    rows={4}
                    fullWidth
                    disabled={uploading}
                    variant="outlined"
                    required
                    error={!moduleForm.description && error}
                    helperText="Provide a detailed description of what users will learn"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2
                      }
                    }}
                  />

                  <Grid container spacing={{ xs: 2, sm: 3 }}>
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth disabled={uploading} variant="outlined" sx={{ width: '16rem' }}>
                        <InputLabel>Course</InputLabel>
                        <Select
                          value={moduleForm.course}
                          onChange={(e) => setModuleForm({ ...moduleForm, course: e.target.value })}
                          label="Course"
                          sx={{ borderRadius: 2 }}
                        >
                          <MenuItem value="">
                            <em>No Course</em>
                          </MenuItem>
                          {courses.map((course) => (
                            <MenuItem key={course.id} value={course.id}>
                              {course.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </Stack>
              </Paper>

              {/* 3D Model Option Selection */}
              <Paper 
                elevation={0}
                sx={{ 
                  p: { xs: 2, sm: 3 }, 
                  border: '1px solid', 
                  borderColor: 'divider',
                  borderRadius: 2
                }}
              >
                <Typography 
                  variant="h6" 
                  fontWeight={600} 
                  gutterBottom
                  sx={{ 
                    fontSize: { xs: '1.125rem', sm: '1.25rem' },
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    mb: 2
                  }}
                >
                  <Model3DIcon color="primary" />
                  3D Model Configuration
                </Typography>

                <Typography 
                  variant="body2" 
                  color="text.secondary" 
                  sx={{ mb: 3, lineHeight: 1.6 }}
                >
                  Choose whether this module will include a 3D model for interactive learning experiences.
                  You can always add a 3D model later.
                </Typography>

                <Grid container spacing={{ xs: 1, sm: 2 }}>
                  <Grid item xs={12} sm={6} sx={{ width: '45rem' }}>
                    <OptionCard
                      title="Basic Module"
                      description="Text, images, and video-based learning content"
                      selected={!moduleForm.has3DModel}
                      onClick={() => handleToggle3DModel(false)}
                      icon={<InfoIcon />}
                      disabled={uploading}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} sx={{ width: '45rem' }}>
                    <OptionCard
                      title="3D Interactive Module"
                      description="Include 3D models with interactive hotspots and animations"
                      selected={moduleForm.has3DModel}
                      onClick={() => handleToggle3DModel(true)}
                      icon={<Model3DIcon />}
                      disabled={uploading}
                    />
                  </Grid>
                </Grid>

                {moduleForm.has3DModel && (
                  <Box sx={{ mt: 3 }}>
                    <FormControl fullWidth disabled={uploading} variant="outlined">
                      <InputLabel>3D Model Type</InputLabel>
                      <Select
                        value={moduleForm.modelType}
                        onChange={(e) => setModuleForm({ ...moduleForm, modelType: e.target.value })}
                        label="3D Model Type"
                        sx={{ borderRadius: 2 }}
                      >
                        <MenuItem value="auto">Auto Detect</MenuItem>
                        <MenuItem value="gltf">GLTF/GLB Format</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                )}
              </Paper>

              {/* Tags Section */}
              <Paper 
                elevation={0}
                sx={{ 
                  p: { xs: 2, sm: 3 }, 
                  border: '1px solid', 
                  borderColor: 'divider',
                  borderRadius: 2
                }}
              >
                <Typography 
                  variant="h6" 
                  fontWeight={600} 
                  gutterBottom
                  sx={{ 
                    fontSize: { xs: '1.125rem', sm: '1.25rem' },
                    mb: 2
                  }}
                >
                  Tags (Optional)
                </Typography>

                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mb: 2 }}>
                  <TextField
                    label="Add Tags"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
                    fullWidth
                    disabled={uploading}
                    variant="outlined"
                    placeholder="Enter tag and press Enter"
                    helperText="Add relevant keywords to help users find this module"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2
                      }
                    }}
                  />
                  <Button
                    onClick={handleAddTag}
                    disabled={uploading || !tagInput.trim()}
                    variant="outlined"
                    startIcon={<AddIcon />}
                    sx={{ 
                      minWidth: { xs: '100%', sm: '120px' },
                      borderRadius: 2,
                      py: 1.5
                    }}
                  >
                    Add Tag
                  </Button>
                </Stack>

                {moduleForm.tags.length > 0 && (
                  <Box 
                    sx={{ 
                      display: 'flex', 
                      flexWrap: 'wrap', 
                      gap: 1,
                      p: 2,
                      bgcolor: alpha(theme.palette.primary.main, 0.05),
                      borderRadius: 2,
                      border: '1px solid',
                      borderColor: alpha(theme.palette.primary.main, 0.2)
                    }}
                  >
                    {moduleForm.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        onDelete={() => handleRemoveTag(tag)}
                        disabled={uploading}
                        color="primary"
                        variant="outlined"
                        sx={{ 
                          borderRadius: 2,
                          '& .MuiChip-label': {
                            px: 1.5
                          }
                        }}
                      />
                    ))}
                  </Box>
                )}
              </Paper>

              {/* Action Buttons */}
              <Stack 
                direction={{ xs: 'column', sm: 'row' }} 
                spacing={2} 
                sx={{ pt: 2 }}
              >
                {moduleForm.has3DModel ? (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => setCurrentTab(1)}
                    disabled={uploading || !moduleForm.name || !moduleForm.description}
                    size="large"
                    startIcon={<Model3DIcon />}
                    sx={{ 
                      borderRadius: 2,
                      py: 1.5,
                      fontSize: '1rem',
                      fontWeight: 600
                    }}
                    fullWidth
                  >
                    Next: Upload 3D Model
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="success"
                    onClick={handleSubmit}
                    disabled={uploading || !moduleForm.name || !moduleForm.description}
                    size="large"
                    startIcon={<CheckCircleIcon />}
                    sx={{ 
                      borderRadius: 2,
                      py: 1.5,
                      fontSize: '1rem',
                      fontWeight: 600
                    }}
                    fullWidth
                  >
                    {uploading ? 'Creating Module...' : 'Create Basic Module'}
                  </Button>
                )}
              </Stack>
            </Stack>
          </TabPanel>
          
          <TabPanel value={currentTab} index={1}>
            <Stack spacing={{ xs: 2, sm: 3 }} sx={{ height: '500px' }}>
              <Paper 
                elevation={0}
                sx={{ 
                  flex: 1,
                  border: '2px dashed', 
                  borderColor: modelPreviewUrl ? 'primary.main' : 'divider',
                  borderRadius: 3,
                  overflow: 'hidden',
                  position: 'relative',
                  minHeight: '350px',
                  transition: 'all 0.3s ease'
                }}
              >
                {!modelPreviewUrl ? (
                  <Box 
                    onClick={() => fileInputRef.current?.click()}
                    sx={{
                      height: '100%',
                      display: 'flex', 
                      flexDirection: 'column',
                      alignItems: 'center', 
                      justifyContent: 'center',
                      gap: 2,
                      cursor: 'pointer',
                      p: { xs: 2, sm: 4 },
                      '&:hover': {
                        bgcolor: uploading ? 'transparent' : alpha(theme.palette.primary.main, 0.05),
                        borderColor: uploading ? 'divider' : 'primary.main'
                      }
                    }}
                  >
                    <Avatar
                      sx={{
                        width: { xs: 80, sm: 100 },
                        height: { xs: 80, sm: 100 },
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: 'primary.main'
                      }}
                    >
                      <CloudUploadIcon sx={{ fontSize: { xs: 40, sm: 50 } }} />
                    </Avatar>
                    <Typography 
                      variant="h6" 
                      color="text.primary"
                      fontWeight={600}
                      sx={{ fontSize: { xs: '1.125rem', sm: '1.25rem' } }}
                    >
                      Upload 3D Model
                    </Typography>
                    <Typography 
                      variant="body1" 
                      color="text.secondary" 
                      align="center"
                      sx={{ maxWidth: 300 }}
                    >
                      Drag and drop your 3D model file here, or click to browse
                    </Typography>
                    <Box sx={{ 
                      display: 'flex', 
                      flexWrap: 'wrap', 
                      gap: 1, 
                      justifyContent: 'center',
                      px: 2
                    }}>
                      {['.GLTF', '.GLB'].map((format) => (
                        <Chip 
                          key={format}
                          label={format} 
                          size="small" 
                          variant="outlined"
                          sx={{ borderRadius: 1 }}
                        />
                      ))}
                    </Box>
                    <Button 
                      variant="contained" 
                      disabled={uploading}
                      startIcon={<UploadIcon />}
                      sx={{ 
                        mt: 1,
                        borderRadius: 2,
                        px: 3
                      }}
                    >
                      Select File
                    </Button>
                  </Box>
                ) : (
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <Box sx={{ flex: 1, position: 'relative' }}>
                      <Canvas 
                        key={canvasKey} 
                        camera={{ position: [0, 0, 5], fov: 60 }}
                        style={{ width: '100%', height: '100%' }}
                        frameloop="demand"
                        gl={{ antialias: true, alpha: true }}
                        onCreated={({ gl }) => {
                          gl.setClearColor('#f5f5f5', 1);
                        }}
                      >
                        {/* Enhanced lighting setup for better visibility */}
                        <ambientLight intensity={0.6} />
                        <directionalLight position={[10, 10, 5]} intensity={1.2} castShadow />
                        <directionalLight position={[-10, -10, -5]} intensity={0.8} />
                        <directionalLight position={[0, 10, 10]} intensity={0.8} />
                        <pointLight position={[0, 10, 0]} intensity={0.7} />
                        <pointLight position={[5, 0, 5]} intensity={0.5} />
                        <pointLight position={[-5, 0, -5]} intensity={0.5} />

                        <Suspense fallback={<primitive object={new THREE.Object3D()} />}>
                          <ModelPreview
                            modelUrl={modelPreviewUrl}
                            onLoadingComplete={handleModelLoadingComplete}
                          />
                        </Suspense>
                        <OrbitControls 
                          makeDefault
                          enablePan={true}
                          enableZoom={true}
                          enableRotate={true}
                          enableDamping={true}
                          dampingFactor={0.05}
                          rotateSpeed={1.0}
                          zoomSpeed={1.5}
                          panSpeed={0.8}
                          autoRotate={false}
                        />
                      </Canvas>
                      
                      {modelPreviewUrl && !modelLoaded && (
                        <Box 
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            bgcolor: alpha('#ffffff', 0.9),
                            zIndex: 10
                          }}
                        >
                          <ModelLoader />
                        </Box>
                      )}
                    </Box>

                    <Divider />
                    
                    <Box 
                      sx={{
                        p: { xs: 2, sm: 3 },
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        bgcolor: 'background.paper'
                      }}
                    >
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography 
                          variant="subtitle1" 
                          fontWeight={600}
                          sx={{ 
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {modelFile?.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {modelFile && `${(modelFile.size / 1024 / 1024).toFixed(2)} MB • ${modelFile.type || 'Unknown format'}`}
                        </Typography>
                      </Box>
                      <Button
                        variant="outlined"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploading}
                        startIcon={<UploadIcon />}
                        sx={{ borderRadius: 2, ml: 2 }}
                      >
                        Change
                      </Button>
                    </Box>
                  </Box>
                )}

                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".gltf,.glb"
                  style={{ display: 'none' }}
                  onChange={handleFileSelect}
                  disabled={uploading}
                />
              </Paper>

              {/* Action Buttons */}
              <Stack 
                direction={{ xs: 'column', sm: 'row' }} 
                spacing={2}
                justifyContent="space-between"
              >
                <Button
                  variant="outlined"
                  onClick={() => setCurrentTab(0)}
                  disabled={uploading}
                  size="large"
                  sx={{ 
                    borderRadius: 2,
                    minWidth: { xs: '100%', sm: '140px' }
                  }}
                >
                  Back to Details
                </Button>
                
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1}>
                  <Button
                    variant="outlined"
                    color="success"
                    onClick={() => {
                      setModuleForm({ ...moduleForm, has3DModel: false });
                      handleSubmit();
                    }}
                    disabled={uploading}
                    size="large"
                    sx={{ 
                      borderRadius: 2,
                      minWidth: { xs: '100%', sm: '160px' }
                    }}
                  >
                    Skip & Create Module
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSubmit}
                    disabled={uploading || !modelFile}
                    size="large"
                    startIcon={uploading ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                    sx={{ 
                      borderRadius: 2,
                      minWidth: { xs: '100%', sm: '180px' }
                    }}
                  >
                    {uploading ? 'Creating...' : 'Create with 3D Model'}
                  </Button>
                </Stack>
              </Stack>
            </Stack>
          </TabPanel>
        </Container>

        {/* Upload Progress */}
        {uploading && (
          <Box sx={{ px: 3, py: 2, bgcolor: 'background.paper', borderTop: '1px solid', borderColor: 'divider' }}>
            <Stack spacing={1} alignItems="center">
              <Typography variant="body1" fontWeight={500} color="primary">
                {moduleForm.has3DModel && modelFile ? 'Uploading module with 3D model...' : 'Creating module...'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {uploadProgress}% complete
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={uploadProgress} 
                sx={{ 
                  width: '100%', 
                  height: 8, 
                  borderRadius: 4,
                  bgcolor: alpha(theme.palette.primary.main, 0.1)
                }}
              />
            </Stack>
          </Box>
        )}

        {/* Error Display */}
        {error && (
          <Alert 
            severity="error" 
            sx={{ 
              mx: 3,
              my: 2,
              borderRadius: 2,
            }}
            icon={<WarningIcon />}
          >
            <Typography variant="body2" fontWeight={500}>
              {error}
            </Typography>
          </Alert>
        )}
      </DialogContent>

      <DialogActions 
        sx={{ 
          p: { xs: 2, sm: 3 }, 
          borderTop: '1px solid',
          borderColor: 'divider',
          bgcolor: 'background.paper'
        }}
      >
        <Button 
          onClick={handleClose} 
          disabled={uploading}
          variant="outlined"
          size="large"
          sx={{ 
            borderRadius: 2,
            minWidth: '120px'
          }}
        >
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default ModuleCreator;
