import CryptoJS from 'crypto-js';

// Wasabi credentials
const ACCESS_KEY = 'OQL1BX7MOF71KL0MM0UM';
const SECRET_KEY = '07e80kBbTY2T9kPr1E43kMiq1PkO6lw9LUDhKeMi';
const REGION = 'ap-southeast-2';
const BUCKET = 'airsmart';

/**
 * Generate a signed URL for Wasabi S3 compatible storage
 * @param {string} objectKey - The object key (path) in the bucket
 * @param {number} expiresIn - Expiration time in seconds (default: 1 hour)
 * @returns {string} - Signed URL
 */
export const getSignedUrl = (objectKey, expiresIn = 3600) => {
  try {
    console.log('🔍 Generating signed URL for object key:', objectKey);

    // Ensure objectKey doesn't start with a slash
    if (objectKey.startsWith('/')) {
      objectKey = objectKey.substring(1);
      console.log('🔍 Removed leading slash:', objectKey);
    }

    // URL encode the object key
    const encodedKey = encodeURIComponent(objectKey).replace(/%20/g, '+');
    console.log('🔍 Encoded key:', encodedKey);

    // Calculate expiration timestamp
    const expires = Math.floor(Date.now() / 1000) + expiresIn;

    // Create the string to sign
    const stringToSign = `GET\n\n\n${expires}\n/${BUCKET}/${encodedKey}`;

    // Create the signature
    const signature = CryptoJS.HmacSHA1(stringToSign, SECRET_KEY);
    const encodedSignature = encodeURIComponent(signature.toString(CryptoJS.enc.Base64));

    // Construct the signed URL
    const signedUrl = `https://s3.${REGION}.wasabisys.com/${BUCKET}/${encodedKey}?AWSAccessKeyId=${ACCESS_KEY}&Expires=${expires}&Signature=${encodedSignature}`;

    console.log('🔍 Generated signed URL:', signedUrl);
    return signedUrl;
  } catch (error) {
    console.error('Error generating signed URL:', error);
    return null;
  }
};

/**
 * Extract object key from a Wasabi URL
 * @param {string} url - The full Wasabi URL
 * @returns {string} - The object key
 */
export const getObjectKeyFromUrl = (url) => {
  try {
    console.log('🔍 Extracting object key from URL:', url);

    // Decode URL first to handle already encoded URLs
    let decodedUrl = url;
    try {
      // Try to decode the URL to handle double-encoding
      decodedUrl = decodeURIComponent(url);
      console.log('🔍 Decoded URL:', decodedUrl);
    } catch (decodeError) {
      console.log('🔍 URL decode failed, using original:', url);
      decodedUrl = url;
    }

    // Handle both URL formats
    if (decodedUrl.includes('wasabisys.com')) {
      // Extract from full URL
      const urlObj = new URL(decodedUrl);
      const pathParts = urlObj.pathname.split('/');
      // Remove empty first element and bucket name
      pathParts.shift(); // Remove empty first element
      pathParts.shift(); // Remove bucket name
      const objectKey = pathParts.join('/');
      console.log('🔍 Extracted object key from full URL:', objectKey);
      return objectKey;
    } else if (decodedUrl.startsWith('/')) {
      // Already an object key with leading slash
      const objectKey = decodedUrl.substring(1);
      console.log('🔍 Object key with leading slash removed:', objectKey);
      return objectKey;
    } else {
      // Already an object key without leading slash
      console.log('🔍 Object key without leading slash:', decodedUrl);
      return decodedUrl;
    }
  } catch (error) {
    console.error('Error extracting object key:', error);
    return url;
  }
};

/**
 * Convert a regular Wasabi URL to a signed URL
 * @param {string} url - The regular Wasabi URL
 * @param {number} expiresIn - Expiration time in seconds
 * @returns {string} - Signed URL
 */
export const convertToSignedUrl = (url, expiresIn = 3600) => {
  const objectKey = getObjectKeyFromUrl(url);
  return getSignedUrl(objectKey, expiresIn);
};
