import api from './apiService';
import { auth } from '../firebase';

// Cache cho user role để tránh gọi API nhiều lần
let userRoleCache = null;
let roleCacheTimestamp = 0;
const ROLE_CACHE_DURATION = 2 * 60 * 1000; // 2 phút

/**
 * Get user role from backend API
 * @returns {Promise<string>} User role
 */
export const getUserRole = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Kiểm tra cache trước
    const now = Date.now();
    if (userRoleCache && userRoleCache.userId === user.uid && (now - roleCacheTimestamp) < ROLE_CACHE_DURATION) {
      return userRoleCache.role;
    }


    // Try to get role from backend API
    try {
      const data = await api.get(`/users/${user.uid}/role`);
      const role = data.role || 'installer';

      // Cache kết quả
      userRoleCache = { userId: user.uid, role };
      roleCacheTimestamp = now;

      return role;
    } catch (apiError) {

      // Initialize user if not exists
      await initializeUser();

      // Try again after initialization
      const data = await api.get(`/users/${user.uid}/role`);
      const role = data.role || 'installer';

      // Cache kết quả
      userRoleCache = { userId: user.uid, role };
      roleCacheTimestamp = now;

      return role;
    }
  } catch (error) {
    console.error('Error getting user role:', error);
    return 'installer'; // Default fallback
  }
};

/**
 * Initialize user in database on first login
 * @returns {Promise<Object>} User profile
 */
export const initializeUser = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }

    const userData = {
      email: user.email,
      displayName: user.displayName || user.email.split('@')[0],
    };

    const data = await api.post(`/users/${user.uid}/initialize`, userData);
    return data;
  } catch (error) {
    console.error('Error initializing user:', error);
    throw error;
  }
};

/**
 * Clear user role cache
 */
export const clearUserRoleCache = () => {
  userRoleCache = null;
  roleCacheTimestamp = 0;
};

/**
 * Set user role (for admin use)
 * @param {string} userId - User ID
 * @param {string} role - Role to assign
 * @returns {Promise<Object>} Result
 */
export const setUserRole = async (userId, role) => {
  try {
    const data = await api.put(`/users/${userId}/role`, { role });

    // Clear cache sau khi update role
    clearUserRoleCache();

    return data;
  } catch (error) {
    console.error('Error setting user role:', error);
    throw error;
  }
};

/**
 * Get user profile with role information
 * @returns {Promise<Object>} User profile
 */
export const getUserProfile = async () => {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get full profile from backend
    try {
      const data = await api.get(`/users/${user.uid}/profile`);
      return data;
    } catch (apiError) {
      // If user not found, initialize and return basic profile
      await initializeUser();
      const data = await api.get(`/users/${user.uid}/profile`);
      return data;
    }
  } catch (error) {
    console.error('Error getting user profile:', error);

    // Fallback to basic profile
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }

    const role = await getUserRole();
    return {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      role: role,
      emailVerified: user.emailVerified,
    };
  }
};

/**
 * Valid user roles
 */
export const USER_ROLES = {
  INSTALLER: 'installer',
  ENGINEER: 'engineer',
  ARCHITECT: 'architect',
  SALESPERSON: 'salesperson',
};

/**
 * Check if role is valid
 * @param {string} role - Role to check
 * @returns {boolean} Whether role is valid
 */
export const isValidRole = (role) => {
  return Object.values(USER_ROLES).includes(role?.toLowerCase());
};
