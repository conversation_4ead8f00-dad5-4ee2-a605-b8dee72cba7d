import Module from '../models/Module';
import ModuleContainer from '../models/ModuleContainer';
import api from './apiService';
import { shouldShowIntroductionCourse } from './userProgressService';


// Cache cho courses với modules để tránh gọi API nhiều lần
let coursesCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 phút

// Cache cho filtered results theo user
let userFilteredCache = new Map();
let userFilteredTimestamps = new Map();

// Cache cho individual modules
let moduleCache = new Map();
let moduleCacheTimestamps = new Map();
const MODULE_CACHE_DURATION = 3 * 60 * 1000; // 3 phút

/**
 * Filter introduction course based on user progress
 * @param {Array} courses - Array of courses
 * @param {string} userId - User ID
 * @returns {Promise<Array>} Filtered courses
 */
async function filterIntroductionCourse(courses, userId) {
  if (!userId) {
    return courses; // Return all courses if no user
  }

  try {
    // Pass courses data to avoid additional API call
    const shouldShow = await shouldShowIntroductionCourse(userId, courses);

    if (shouldShow) {
      return courses; // Show all courses including introduction
    } else {
      // Filter out introduction course
      return courses.filter(course => {
        // Filter out courses with introduction-related names/IDs
        const isIntroduction =
          course.id === 'introduction' ||
          course.name?.toLowerCase().includes('introduction') ||
          course.name?.toLowerCase().includes('platform guide') ||
          course.description?.toLowerCase().includes('introduction');

        return !isIntroduction;
      });
    }
  } catch (error) {
    console.error('Error filtering introduction course:', error);
    return courses; // Return all courses if error
  }
}

/**
 * Fetch courses with modules from the API (NEW - Dynamic approach)
 * @param {string} userId - User ID for filtering introduction course
 * @returns {Promise<Array>} Array of ModuleContainer instances and standalone Module instances
 */
export async function fetchCoursesWithModules(userId = null) {
  try {
    const now = Date.now();

    // Check user-specific cache first
    if (userId) {
      const userCacheKey = userId;
      const userCacheTime = userFilteredTimestamps.get(userCacheKey);
      if (userFilteredCache.has(userCacheKey) && userCacheTime && (now - userCacheTime) < CACHE_DURATION) {
        return userFilteredCache.get(userCacheKey);
      }
    }

    // Check general cache for raw data
    if (coursesCache && (now - cacheTimestamp) < CACHE_DURATION) {
      const rawData = coursesCache;

      if (userId) {
        // Filter and cache for this user
        const filteredResult = await filterIntroductionCourse(rawData, userId);
        userFilteredCache.set(userId, filteredResult);
        userFilteredTimestamps.set(userId, now);
        return filteredResult;
      }

      return rawData;
    }

    const data = await api.get('/module/courses');

    const result = [];

    // Process each course and standalone module
    data.forEach(item => {
      if (item.type === 'container' && item.isContainer) {
        // This is a course/container - ONLY show if isActive is true
        if (item.isActive === false) {
          return; // Skip inactive courses
        }

        // Filter out locked modules from the course
        const availableModules = item.modules.filter(module => !module.isLocked);

        const container = new ModuleContainer({
          id: item.id,
          name: item.name,
          description: item.description,
          thumbnail: item.thumbnail,
          modules: availableModules.map(module => new Module(module)),
          totalModules: item.totalModules || item.modules.length,
          totalSteps: item.totalSteps,
          totalQuizQuestions: item.totalQuizQuestions,
          tags: item.tags || [],
          createdAt: item.createdAt ? new Date(item.createdAt) : new Date(),
          updatedAt: item.updatedAt ? new Date(item.updatedAt) : new Date(),
          isContainer: true,
          isComingSoon: item.isComingSoon || false
        });

        result.push(container);
      } else {
        // This is a standalone module - only add if not locked
        if (!item.isLocked) {
          result.push(new Module(item));
        }
      }
    });

    // Cache raw data
    coursesCache = result;
    cacheTimestamp = now;

    // Filter introduction course based on user progress
    if (userId) {
      const filteredResult = await filterIntroductionCourse(result, userId);

      // Cache filtered result for this user
      userFilteredCache.set(userId, filteredResult);
      userFilteredTimestamps.set(userId, now);

      return filteredResult;
    }

    return result;
  } catch (error) {
    console.error('Error fetching courses with modules:', error);
    throw error;
  }
}

/**
 * Clear courses cache
 */
export function clearCoursesCache() {
  coursesCache = null;
  cacheTimestamp = 0;
  // Also clear user-specific caches
  userFilteredCache.clear();
  userFilteredTimestamps.clear();
}

/**
 * Clear module cache
 */
export function clearModuleCache(moduleId = null) {
  if (moduleId) {
    moduleCache.delete(moduleId);
    moduleCacheTimestamps.delete(moduleId);
  } else {
    moduleCache.clear();
    moduleCacheTimestamps.clear();
  }
}

/**
 * Fetch modules in the original format (for backward compatibility)
 * @returns {Promise<Array>} Array of Module instances only
 */
export async function fetchModulesOriginal() {
  try {
    const data = await api.get('/module');
    return data.map(item => new Module(item));
  } catch (error) {
    console.error('Error fetching modules:', error);
    throw error;
  }
}

/**
 * Fetch a module by ID from the API
 * @param {string} moduleId - The module ID
 * @returns {Promise<Module>} Module instance
 */
export async function fetchModuleById(moduleId) {
  try {
    const data = await api.get(`/module/${moduleId}`);
    return new Module(data);
  } catch (error) {
    console.error('Error fetching module:', error);
    throw error;
  }
}

/**
 * Get a module by ID from API
 * @param {string} moduleId - The module ID
 * @returns {Promise<Object|null>} Module data or null if not found
 */
export async function getModule(moduleId) {
  try {
    // Kiểm tra cache trước
    const now = Date.now();
    const cacheTimestamp = moduleCacheTimestamps.get(moduleId);

    if (moduleCache.has(moduleId) && cacheTimestamp && (now - cacheTimestamp) < MODULE_CACHE_DURATION) {
      return moduleCache.get(moduleId);
    }

    const data = await api.get(`/module/${moduleId}`);

    // Cache kết quả
    if (data) {
      moduleCache.set(moduleId, data);
      moduleCacheTimestamps.set(moduleId, now);
    }

    return data || null;
  } catch (error) {
    console.error("Error getting module:", error);
    throw error;
  }
}

/**
 * Get all steps for a module from API
 * @param {string} moduleId - The module ID
 * @returns {Promise<Array>} Array of step objects
 */
export async function getModuleSteps(moduleId) {
  try {
    const data = await api.get(`/module/${moduleId}/steps`);
    return data || [];
  } catch (error) {
    console.error("Error getting module steps:", error);
    throw error;
  }
}

/**
 * Get all quiz questions for a module from API
 * @param {string} moduleId - The module ID
 * @returns {Promise<Array>} Array of quiz question objects
 */
export async function getModuleQuiz(moduleId) {
  try {
    const data = await api.get(`/quiz/${moduleId}/questions`);
    return data || [];
  } catch (error) {
    console.error("Error getting module quiz:", error);
    throw error;
  }
}

/**
 * Get the next module after the current one
 * @param {string} currentModuleId - The current module ID
 * @returns {Promise<Object|null>} Next module data or null if no next module
 */
export async function getNextModule(currentModuleId) {
  try {
    // Get all modules as raw data (not Module instances)
    const allModulesData = await api.get('/module');

    // Find current module index
    const currentIndex = allModulesData.findIndex(module => module.id === currentModuleId);

    // Return next module if exists
    if (currentIndex !== -1 && currentIndex < allModulesData.length - 1) {
      return allModulesData[currentIndex + 1];
    }

    return null; // No next module
  } catch (error) {
    console.error("Error getting next module:", error);
    throw error;
  }
}

/**
 * Get the next uncompleted module
 * @param {string} currentModuleId - Current module ID
 * @param {Function} isModuleCompleted - Function to check if module is completed
 * @returns {Object|null} Next uncompleted module or null if none found
 */
export async function getNextUncompletedModule(currentModuleId, isModuleCompleted, userId = null) {
  try {
    // Gọi trực tiếp API thay vì import để tránh vòng lặp
    const modules = await fetchCoursesWithModules(userId);

    // Flatten all modules from containers and standalone modules
    const allModules = [];
    modules.forEach(item => {
      if (item.isContainer && item.modules) {
        allModules.push(...item.modules);
      } else if (!item.isContainer) {
        allModules.push(item);
      }
    });

    // Find current module index
    const currentIndex = allModules.findIndex(module => module.id === currentModuleId);

    if (currentIndex === -1) {
      return null;
    }

    // Find next uncompleted module starting from the next module
    for (let i = currentIndex + 1; i < allModules.length; i++) {
      const module = allModules[i];
      if (!isModuleCompleted(module.id)) {
        return module;
      }
    }
    return null;
  } catch (error) {
    console.error('Error getting next uncompleted module:', error);
    return null;
  }
}
