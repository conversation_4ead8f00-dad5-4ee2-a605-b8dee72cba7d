import api from './apiService';

/**
 * Transform API question data to frontend format
 * @param {Object} apiQuestion - Question data from API
 * @returns {Object} - Transformed question data
 */
const transformQuestionData = (apiQuestion) => {
  try {
    // Handle both old and new data structures
    let correctAnswerArray = [];
    let questionType = 'single'; // Default to single

    // Check if it's new structure with questionType field
    if (apiQuestion.questionType) {
      questionType = apiQuestion.questionType;
      correctAnswerArray = Array.isArray(apiQuestion.correctAnswer) 
        ? apiQuestion.correctAnswer 
        : [apiQuestion.correctAnswer];
    } else {
      // Old structure - single answer
      correctAnswerArray = [apiQuestion.correctAnswer];
    }

    // Create options with id and text properties
    const options = apiQuestion.options.map((option, index) => ({
      id: `option${index + 1}`,
      text: option,
      isCorrect: correctAnswerArray.includes(index)
    }));

    const transformedQuestion = {
      id: apiQuestion.id,
      text: apiQuestion.question,
      type: questionType, // 'single' or 'multiple'
      options: options,
      explanation: apiQuestion.explanation || '',
      order: apiQuestion.order || 0
    };

    return transformedQuestion;
  } catch (error) {
    console.error("Error transforming question data:", error, apiQuestion);
    // Return a default structure to prevent crashes
    return {
      id: apiQuestion.id || "unknown",
      text: apiQuestion.question || "Error loading question",
      type: 'single',
      options: [
        { id: 'option1', text: 'Error loading options', isCorrect: true }
      ],
      explanation: '',
      order: 0
    };
  }
};

/**
 * Get quiz questions for a specific module
 * @param {string} moduleId - The ID of the module
 * @returns {Promise<Array>} - Array of quiz questions
 */
export const getQuizQuestions = async (moduleId) => {
  try {
    const data = await api.get(`/quiz/${moduleId}/questions`);

    if (!data || data.length === 0) {
      return [];
    }

    // Transform API data to frontend format
    const transformedData = data.map(transformQuestionData);
    return transformedData;
  } catch (error) {
    console.error('Error getting quiz questions:', error);
    throw error;
  }
};

/**
 * Get a specific quiz question
 * @param {string} moduleId - The ID of the module
 * @param {string} questionId - The ID of the question
 * @returns {Promise<Object>} - The question object
 */
export const getQuizQuestion = async (moduleId, questionId) => {
  try {
    const data = await api.get(`/quiz/${moduleId}/questions/${questionId}`);

    if (!data) {
      return null;
    }

    // Transform API data to frontend format
    return transformQuestionData(data);
  } catch (error) {
    console.error('Error getting quiz question:', error);
    throw error;
  }
};

/**
 * Get the passing score for a module's quiz
 * @param {string} moduleId - The ID of the module
 * @returns {Promise<number>} - The passing score (default: 70)
 */
export const getQuizPassingScore = async (moduleId) => {
  try {
    const data = await api.get(`/quiz/${moduleId}/passing-score`);
    return data?.passingScore || 70;
  } catch (error) {
    console.error('Error getting quiz passing score:', error);
    return 70; // Default passing score in case of error
  }
};

/**
 * Check if a user has passed a module's quiz
 * @param {string} userId - The ID of the user
 * @param {string} moduleId - The ID of the module
 * @returns {Promise<boolean>} - Whether the user has passed the quiz
 */
export const hasPassedQuiz = async (userId, moduleId) => {
  try {
    const progress = await api.get(`/user-progress/${userId}/module/${moduleId}`);

    if (!progress) {
      return false; // No progress record means not passed
    }

    const passingScore = await getQuizPassingScore(moduleId);

    // Check if the user has completed the module and has a score >= passing score
    return progress.completed && progress.quizScore >= passingScore;
  } catch (error) {
    console.error('Error checking if user passed quiz:', error);
    return false; // Default to not passed in case of error
  }
};

/**
 * Get correct answers and explanations for all quiz questions in a module
 * @param {string} moduleId - The ID of the module
 * @returns {Promise<Array>} - Array of {id, correctAnswer, explanation}
 */
export const getQuizAnswers = async (moduleId) => {
  try {
    const data = await api.get(`/quiz/${moduleId}/answers`);
    return data;
  } catch (error) {
    console.error('Error getting quiz answers:', error);
    throw error;
  }
};
