import api from './apiService';
import cacheService from './cacheService';

/**
 * Get all progress for a specific user
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} - Array of module progress objects
 */
export const getUserProgress = async (userId) => {
  try {
    // Sử dụng cache với TTL ngắn hơn cho user progress (2 phút)
    const cacheKey = cacheService.createKey('user_progress', userId);

    const data = await cacheService.getOrSet(
      cacheKey,
      () => api.get(`/user-progress/${userId}`),
      2 * 60 * 1000 // 2 phút
    );

    return data || {};
  } catch (error) {
    console.error("Error getting user progress:", error);
    throw error;
  }
};

/**
 * Get progress for a specific module
 * @param {string} userId - The user ID
 * @param {string} moduleId - The module ID
 * @returns {Promise<Object|null>} - Module progress object or null if not found
 */
export const getModuleProgress = async (userId, moduleId) => {
  try {
    // Sử dụng cache với TTL ngắn hơn cho module progress (1 phút)
    const cacheKey = cacheService.createKey('module_progress', userId, moduleId);

    const data = await cacheService.getOrSet(
      cacheKey,
      () => api.get(`/user-progress/${userId}/module/${moduleId}`),
      1 * 60 * 1000 // 1 phút
    );

    return data || null;
  } catch (error) {
    console.error("Error getting module progress:", error);
    throw error;
  }
};

/**
 * Update or create progress for a specific module
 * @param {string} userId - The user ID
 * @param {string} moduleId - The module ID
 * @param {string} stepId - The current step ID
 * @param {boolean} completed - Whether the module is completed
 * @param {number|null} quizScore - The quiz score (null if not taken)
 * @returns {Promise<Object>} - Updated progress data
 */
export const updateModuleProgress = async (userId, moduleId, stepId, completed = false, quizScore = null) => {
  try {
    const updateData = {
      currentStepId: stepId, // Sử dụng currentStepId để đồng bộ với backend
      completed,
      quizScore
    };

    // Backend will handle quizPassed logic based on module's passing score
    // Don't set quizPassed here - let backend decide

    const data = await api.post(`/user-progress/${userId}/module/${moduleId}/update`, updateData);

    // Clear cache sau khi update để đảm bảo data mới được fetch
    const userProgressCacheKey = cacheService.createKey('user_progress', userId);
    const moduleProgressCacheKey = cacheService.createKey('module_progress', userId, moduleId);

    cacheService.delete(userProgressCacheKey);
    cacheService.delete(moduleProgressCacheKey);
    return data;
  } catch (error) {
    console.error("Error updating module progress:", error);
    throw error;
  }
};

/**
 * Check if user has any progress data
 * @param {string} userId - The user ID
 * @returns {Promise<boolean>} - True if user has progress, false if empty
 */
export const hasUserProgress = async (userId) => {
  try {
    const progress = await getUserProgress(userId);

    // Check if progress object is empty or has no meaningful data
    if (!progress || typeof progress !== 'object') {
      return false;
    }

    // Check if there are any module progress entries
    const moduleIds = Object.keys(progress);
    return moduleIds.length > 0;
  } catch (error) {
    console.error("Error checking user progress:", error);
    return false; // Default to false if error occurs
  }
};

/**
 * Check if user should see introduction course
 * @param {string} userId - The user ID
 * @param {Array} coursesData - Optional courses data to avoid API call
 * @returns {Promise<boolean>} - True if should show introduction, false otherwise
 */
export const shouldShowIntroductionCourse = async (userId, coursesData = null) => {
  try {
    const progress = await getUserProgress(userId);

    // Check if progress object is empty or has no meaningful data
    if (!progress || typeof progress !== 'object') {
      return true; // Show introduction for new users
    }

    // Check if there are any module progress entries
    const moduleIds = Object.keys(progress);
    if (moduleIds.length === 0) {
      return true; // Show introduction if no progress at all
    }

    // Get introduction course modules from provided data or API
    let introModuleIds = [];
    if (coursesData) {
      // Use provided courses data to avoid API call
      const introCourse = coursesData.find(course =>
        course.id === 'introduction' ||
        course.name?.toLowerCase().includes('introduction') ||
        course.name?.toLowerCase().includes('platform guide') ||
        course.description?.toLowerCase().includes('introduction')
      );

      if (introCourse && introCourse.modules) {
        introModuleIds = introCourse.modules.map(module => module.id);
      }
    }

    // If introduction course exists and has modules
    if (introModuleIds.length > 0) {
      // Check if ALL introduction modules are completed
      const allIntroCompleted = introModuleIds.every(moduleId =>
        progress[moduleId] && progress[moduleId].completed === true
      );

      if (allIntroCompleted) {
        return false; // Don't show if all introduction modules are completed
      }
    }

    // If user has progress on other (non-introduction) modules, don't show introduction
    // (user already started learning without introduction)
    const hasOtherProgress = moduleIds.some(id => {
      const isIntroModule = introModuleIds.includes(id);
      return !isIntroModule && progress[id];
    });

    if (hasOtherProgress) {
      return false;
    }

    // Show introduction if user has no progress or only incomplete introduction progress
    return true;
  } catch (error) {
    console.error("Error checking introduction course visibility:", error);
    return false; // Default to false if error occurs
  }
};

/**
 * Check if a module is unlocked for a user
 * @param {string} userId - The user ID
 * @param {string} moduleId - The module ID to check
 * @param {Array} allModules - All modules in order
 * @returns {Promise<boolean>} - Whether the module is unlocked
 */
export const isModuleUnlocked = async (userId, moduleId, allModules) => {
  try {
    // First module is always unlocked
    const moduleIndex = allModules.findIndex(module => module.id === moduleId);
    if (moduleIndex === 0) return true;

    // For other modules, check if previous module is completed
    const previousModule = allModules[moduleIndex - 1];
    if (!previousModule) return false;

    const previousModuleProgress = await getModuleProgress(userId, previousModule.id);

    // Chỉ cần kiểm tra completed để đồng bộ với logic trong useUserProgress
    return previousModuleProgress?.completed === true;
  } catch (error) {
    console.error("Error checking if module is unlocked:", error);
    return false;
  }
};
