/**
 * Cache service để quản lý cache API calls và tránh gọi API nhiều lần
 */

class CacheService {
  constructor() {
    this.cache = new Map();
    this.timestamps = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5 phút
  }

  /**
   * Tạo cache key từ các tham số
   * @param {string} prefix - Prefix cho cache key
   * @param {...any} params - <PERSON><PERSON><PERSON> tham số để tạo key
   * @returns {string} Cache key
   */
  createKey(prefix, ...params) {
    return `${prefix}_${params.join('_')}`;
  }

  /**
   * Lưu dữ liệu vào cache
   * @param {string} key - Cache key
   * @param {any} data - Dữ liệu cần cache
   * @param {number} ttl - Time to live (ms), mặc định 5 phút
   */
  set(key, data, ttl = this.defaultTTL) {
    this.cache.set(key, data);
    this.timestamps.set(key, Date.now() + ttl);
  }

  /**
   * <PERSON><PERSON>y dữ liệu từ cache
   * @param {string} key - <PERSON><PERSON> key
   * @returns {any|null} Dữ liệu từ cache hoặc null nếu không tồn tại/hết hạn
   */
  get(key) {
    const timestamp = this.timestamps.get(key);
    
    // Kiểm tra xem cache có hết hạn không
    if (!timestamp || Date.now() > timestamp) {
      this.delete(key);
      return null;
    }

    return this.cache.get(key);
  }

  /**
   * Xóa một cache entry
   * @param {string} key - Cache key
   */
  delete(key) {
    this.cache.delete(key);
    this.timestamps.delete(key);
  }

  /**
   * Xóa tất cả cache entries có prefix
   * @param {string} prefix - Prefix cần xóa
   */
  deleteByPrefix(prefix) {
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix)) {
        this.delete(key);
      }
    }
  }

  /**
   * Xóa toàn bộ cache
   */
  clear() {
    this.cache.clear();
    this.timestamps.clear();
  }

  /**
   * Kiểm tra xem cache có tồn tại và còn hạn không
   * @param {string} key - Cache key
   * @returns {boolean} True nếu cache tồn tại và còn hạn
   */
  has(key) {
    const timestamp = this.timestamps.get(key);
    
    if (!timestamp || Date.now() > timestamp) {
      this.delete(key);
      return false;
    }

    return this.cache.has(key);
  }

  /**
   * Lấy hoặc tạo cache với function
   * @param {string} key - Cache key
   * @param {Function} fetchFn - Function để fetch dữ liệu nếu cache không tồn tại
   * @param {number} ttl - Time to live (ms)
   * @returns {Promise<any>} Dữ liệu từ cache hoặc từ fetchFn
   */
  async getOrSet(key, fetchFn, ttl = this.defaultTTL) {
    const cached = this.get(key);
    
    if (cached !== null) {
      return cached;
    }
    try {
      const data = await fetchFn();
      this.set(key, data, ttl);
      return data;
    } catch (error) {
      console.error(`Error fetching data for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Dọn dẹp cache hết hạn
   */
  cleanup() {
    const now = Date.now();
    
    for (const [key, timestamp] of this.timestamps.entries()) {
      if (now > timestamp) {
        this.delete(key);
      }
    }
  }

  /**
   * Lấy thông tin về cache (để debug)
   * @returns {Object} Thông tin cache
   */
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      timestamps: Object.fromEntries(this.timestamps)
    };
  }
}

// Tạo instance singleton
const cacheService = new CacheService();

// Dọn dẹp cache mỗi 10 phút
setInterval(() => {
  cacheService.cleanup();
}, 10 * 60 * 1000);

export default cacheService;
