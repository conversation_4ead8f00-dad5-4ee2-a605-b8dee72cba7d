import api from './apiService';

/**
 * Get files by user role
 * @param {string} role - User role (installer, engineer, architect, salesperson)
 * @returns {Promise<Object>} Files data for the role
 */
export const getFilesByRole = async (role) => {
  try {
    const data = await api.get(`/files/role/${role}`);
    return data;
  } catch (error) {
    console.error("Error getting files by role:", error);
    throw error;
  }
};

/**
 * Get file metadata and signed URL
 * @param {string} role - User role
 * @param {string} fileName - File name
 * @returns {Promise<Object>} File metadata with signed URL
 */
export const getFile = async (role, fileName) => {
  try {
    const data = await api.get(`/files/role/${role}/file/${fileName}`);
    return data;
  } catch (error) {
    console.error("Error getting file:", error);
    throw error;
  }
};

/**
 * Get signed URL for file
 * @param {string} role - User role
 * @param {string} fileName - File name
 * @param {number} expiresIn - URL expiration time in seconds
 * @returns {Promise<Object>} Signed URL data
 */
export const getFileUrl = async (role, fileName, expiresIn = 3600) => {
  try {
    const data = await api.get(`/files/role/${role}/file/${fileName}/url?expires=${expiresIn}`);
    return data;
  } catch (error) {
    console.error("Error getting file URL:", error);
    throw error;
  }
};

/**
 * Get file type from file name
 * @param {string} fileName - File name
 * @returns {string} File type
 */
export const getFileType = (fileName) => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  const fileTypes = {
    // Documents
    pdf: 'pdf',
    doc: 'document',
    docx: 'document',
    txt: 'document',
    rtf: 'document',
    
    // Images
    jpg: 'image',
    jpeg: 'image',
    png: 'image',
    gif: 'image',
    bmp: 'image',
    svg: 'image',
    
    // Videos
    mp4: 'video',
    avi: 'video',
    mov: 'video',
    wmv: 'video',
    flv: 'video',
    webm: 'video',
    
    // Audio
    mp3: 'audio',
    wav: 'audio',
    flac: 'audio',
    aac: 'audio',
    
    // Archives
    zip: 'archive',
    rar: 'archive',
    '7z': 'archive',
    tar: 'archive',
    gz: 'archive',
    
    // Spreadsheets
    xls: 'spreadsheet',
    xlsx: 'spreadsheet',
    csv: 'spreadsheet',
    
    // Presentations
    ppt: 'presentation',
    pptx: 'presentation',
  };
  
  return fileTypes[extension] || 'unknown';
};

/**
 * Format file size
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Check if file can be previewed in browser
 * @param {string} fileName - File name
 * @returns {boolean} Whether file can be previewed
 */
export const canPreviewFile = (fileName) => {
  const fileType = getFileType(fileName);
  const previewableTypes = ['pdf', 'image', 'video', 'audio'];
  return previewableTypes.includes(fileType);
};
