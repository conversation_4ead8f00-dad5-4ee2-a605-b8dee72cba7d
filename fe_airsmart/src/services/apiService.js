import axios from 'axios';
import cacheService from './cacheService';
import { auth } from '../firebase';

// Base URL for API calls
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000';

// Danh sách các endpoint nên được cache
const CACHEABLE_ENDPOINTS = [
  '/module/courses',
  '/module',
  '/quiz'
];

// Kiểm tra xem endpoint có nên được cache không
const shouldCache = (url, method) => {
  return method === 'GET' && CACHEABLE_ENDPOINTS.some(endpoint => url.includes(endpoint));
};

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication and caching
api.interceptors.request.use(
  async (config) => {
    // Log API calls để debug (giảm log spam)
    if (!config.url.includes('/user-progress/')) {
    }

    // Kiểm tra cache cho GET requests
    if (shouldCache(config.url, config.method)) {
      const cacheKey = cacheService.createKey('api', config.method, config.url);
      const cachedData = cacheService.get(cacheKey);

      if (cachedData) {
        // Trả về cached data thông qua một promise resolved
        config._cached = true;
        config._cachedData = cachedData;
      }
    }

    // Get Firebase auth token if user is authenticated
    const user = auth.currentUser;
    if (user) {
      try {
        const idToken = await user.getIdToken();
        config.headers.Authorization = `Bearer ${idToken}`;
      } catch (error) {
        console.error('Error getting Firebase auth token:', error);
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling and caching
api.interceptors.response.use(
  (response) => {
    // Kiểm tra xem có phải cached response không
    if (response.config._cached) {
      return response.config._cachedData;
    }

    // Log successful responses (giảm log spam)
    if (!response.config.url.includes('/user-progress/')) {
    }

    // Cache response nếu cần
    if (shouldCache(response.config.url, response.config.method)) {
      const cacheKey = cacheService.createKey('api', response.config.method, response.config.url);
      cacheService.set(cacheKey, response.data, 5 * 60 * 1000); // Cache 5 phút
    }

    return response.data;
  },
  (error) => {
    // Handle specific error cases
    if (error.response) {
      // Server responded with an error status
      console.error('API Error:', error.response.data);

      // Handle authentication errors
      if (error.response.status === 401) {
        // Redirect to login or refresh token
        console.warn('Authentication error, redirecting to login...');
      }
    } else if (error.request) {
      // Request was made but no response received
      console.error('No response received:', error.request);
    } else {
      // Error in setting up the request
      console.error('Request error:', error.message);
    }

    return Promise.reject(error);
  }
);

// Utility functions để quản lý cache
export const clearApiCache = () => {
  cacheService.clear();
};

export const clearCacheByPrefix = (prefix) => {
  cacheService.deleteByPrefix(prefix);
};

export const getCacheStats = () => {
  return cacheService.getStats();
};

export default api;
