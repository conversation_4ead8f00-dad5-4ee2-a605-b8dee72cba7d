import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '../auth/auth.context';
import { getUserProgress } from '../services/userProgressService';
import cacheService from '../services/cacheService';

// Thời gian cache (15 phút) - tăng thời gian cache để giảm tần suất gọi API
const CACHE_DURATION = 15 * 60 * 1000;
// Thời gian debounce cho refresh events
const DEBOUNCE_DELAY = 1000;

/**
 * Hook to get and manage user progress
 * @returns {Object} User progress data and functions
 */
export const useUserProgress = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userProgress, setUserProgress] = useState({});

  // Sử dụng ref để lưu trữ thời gian cập nhật cuối cùng
  const lastUpdateRef = useRef(0);
  // <PERSON>ử dụng ref để theo dõi xem đã đang tải dữ liệu chưa
  const isLoadingRef = useRef(false);
  // Ref để theo dõi debounce timer
  const debounceTimerRef = useRef(null);

  // Load all user progress
  const loadUserProgress = useCallback(async (force = false) => {
    if (!currentUser) {
      setLoading(false);
      setUserProgress({});
      return;
    }

    // Nếu đang tải dữ liệu, không tải lại
    if (isLoadingRef.current) {
      return;
    }

    // Kiểm tra xem có cần tải lại dữ liệu không
    const now = Date.now();
    if (!force && now - lastUpdateRef.current < CACHE_DURATION) {
      setLoading(false);
      return;
    }

    try {
      isLoadingRef.current = true;
      setLoading(true);

      const progress = await getUserProgress(currentUser.uid);

      setUserProgress(progress);
      setError(null);

      // Cập nhật thời gian tải dữ liệu
      lastUpdateRef.current = now;
    } catch (err) {
      console.error("Error loading user progress:", err);
      setError(err.message);
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [currentUser]);

  useEffect(() => {
    loadUserProgress();

    // Debounce refresh events để tránh gọi nhiều lần
    const handleRefreshEvent = () => {

      // Clear existing timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // Set new timer with longer debounce
      debounceTimerRef.current = setTimeout(() => {
        loadUserProgress(true);
        debounceTimerRef.current = null;
      }, DEBOUNCE_DELAY);
    };

    window.addEventListener('refreshUserProgress', handleRefreshEvent);

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
      window.removeEventListener('refreshUserProgress', handleRefreshEvent);
    };
  }, [loadUserProgress]);

  /**
   * Get progress for a specific module
   * @param {string} moduleId - The module ID
   * @returns {Object|null} Module progress or null if not found
   */
  const getProgress = (moduleId) => {
    return userProgress[moduleId] || null;
  };

  /**
   * Check if a module is completed
   * @param {string} moduleId - The module ID
   * @returns {boolean} Whether the module is completed
   */
  const isModuleCompleted = (moduleId) => {
    const progress = getProgress(moduleId);
    // Module được coi là hoàn thành nếu completed = true
    // quizPassed là optional, chỉ cần khi có quiz
    return progress?.completed === true;
  };

  /**
   * Get the current step ID for a module
   * @param {string} moduleId - The module ID
   * @returns {string|null} Current step ID or null if not started
   */
  const getCurrentStepId = (moduleId) => {
    const progress = getProgress(moduleId);
    // Kiểm tra cả hai trường có thể chứa step ID
    return progress?.currentStepId || progress?.stepId || null;
  };

  /**
   * Check if a module is in progress (started but not completed)
   * @param {string} moduleId - The module ID
   * @returns {boolean} Whether the module is in progress
   */
  const isModuleInProgress = (moduleId) => {
    const progress = getProgress(moduleId);
    if (!progress) return false;
    
    // Module is in progress if:
    // 1. Not completed AND
    // 2. Has a currentStepId (meaning user has started) AND
    // 3. currentStepId is not 'completed'
    const hasStarted = progress.currentStepId || progress.stepId;
    const notCompleted = !progress.completed;
    const stepIdNotCompleted = hasStarted && hasStarted !== 'completed';
    
    return notCompleted && stepIdNotCompleted;
  };

  /**
   * Get the quiz score for a module
   * @param {string} moduleId - The module ID
   * @returns {number|null} Quiz score or null if not taken
   */
  const getQuizScore = (moduleId) => {
    const progress = getProgress(moduleId);
    return progress?.quizScore || null;
  };

  /**
   * Force refresh user progress data
   */
  const refreshProgress = useCallback(() => {

    // Clear cache trước khi refresh
    if (currentUser) {
      const userProgressCacheKey = cacheService.createKey('user_progress', currentUser.uid);
      cacheService.delete(userProgressCacheKey);
      cacheService.deleteByPrefix(`module_progress_${currentUser.uid}`);
    }

    loadUserProgress(true);
  }, [loadUserProgress, currentUser]);

  return {
    loading,
    error,
    userProgress,
    getProgress,
    isModuleCompleted,
    getCurrentStepId,
    isModuleInProgress,
    getQuizScore,
    refreshProgress
  };
};
