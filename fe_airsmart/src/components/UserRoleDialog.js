import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
} from '@mui/material';
import {
  Close as CloseIcon,
  Person as PersonIcon,
  Build as BuildIcon,
  Engineering as EngineeringIcon,
  Architecture as ArchitectureIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import { getUserProfile, setUserRole, USER_ROLES } from '../services/userService';
import { useAuth } from '../auth/auth.context';

const UserRoleDialog = ({ open, onClose, onRoleUpdated }) => {
  const { currentUser } = useAuth();
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState(null);
  const [selectedRole, setSelectedRole] = useState('');

  // Load user profile when dialog opens
  useEffect(() => {
    if (open && currentUser) {
      loadUserProfile();
    }
  }, [open, currentUser]);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      const profile = await getUserProfile();
      setUserProfile(profile);
      setSelectedRole(profile.role);
    } catch (err) {
      console.error('Error loading user profile:', err);
      setError('Failed to load user profile');
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = (event) => {
    setSelectedRole(event.target.value);
  };

  const handleUpdateRole = async () => {
    if (!selectedRole || selectedRole === userProfile?.role) {
      return;
    }

    try {
      setUpdating(true);
      setError(null);

      await setUserRole(currentUser.uid, selectedRole);

      // Reload profile to get updated data
      await loadUserProfile();

      // Notify parent component
      if (onRoleUpdated) {
        onRoleUpdated(selectedRole);
      }

      // Show success message
      setError(null);
    } catch (err) {
      console.error('Error updating role:', err);
      setError('Failed to update role. Please try again.');
    } finally {
      setUpdating(false);
    }
  };

  const getRoleIcon = (role) => {
    const iconMap = {
      installer: <BuildIcon color="primary" />,
      engineer: <EngineeringIcon color="secondary" />,
      architect: <ArchitectureIcon color="info" />,
      salesperson: <BusinessIcon color="success" />,
    };

    return iconMap[role] || <PersonIcon />;
  };

  const getRoleColor = (role) => {
    const colorMap = {
      installer: 'primary',
      engineer: 'secondary',
      architect: 'info',
      salesperson: 'success',
    };

    return colorMap[role] || 'default';
  };

  const formatRoleName = (role) => {
    const nameMap = {
      installer: 'Installer',
      engineer: 'Engineer',
      architect: 'Architect',
      salesperson: 'Sales Person',
    };

    return nameMap[role] || role;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      sx={{
        zIndex: 1400
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PersonIcon color="primary" />
            <Typography variant="h6">
              User Information
            </Typography>
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {/* Loading State */}
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {/* Error State */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* User Profile */}
        {!loading && userProfile && (
          <Box sx={{ mb: 3 }}>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Email
              </Typography>
              <Typography variant="body1">
                {userProfile.email}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Display Name
              </Typography>
              <Typography variant="body1">
                {userProfile.displayName || 'Not set'}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Current Role
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                {getRoleIcon(userProfile.role)}
                <Chip
                  label={formatRoleName(userProfile.role)}
                  color={getRoleColor(userProfile.role)}
                  variant="outlined"
                />
              </Box>
            </Box>
          </Box>
        )}

        {/* Role Selection */}
        {/* {!loading && userProfile && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Change Role
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Select Role</InputLabel>
              <Select
                value={selectedRole}
                onChange={handleRoleChange}
                label="Select Role"
              >
                {Object.values(USER_ROLES).map((role) => (
                  <MenuItem key={role} value={role}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getRoleIcon(role)}
                      {formatRoleName(role)}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Role Descriptions:</strong>
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                • <strong>Installer:</strong> Access to installation guides and tools<br/>
                • <strong>Engineer:</strong> Access to technical specifications and diagrams<br/>
                • <strong>Architect:</strong> Access to design guidelines and CAD templates<br/>
                • <strong>Sales Person:</strong> Access to brochures and pricing information
              </Typography>
            </Alert>
          </Box>
        )} */}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        {!loading && userProfile && selectedRole !== userProfile.role && (
          <Button
            onClick={handleUpdateRole}
            variant="contained"
            disabled={updating}
            startIcon={updating ? <CircularProgress size={16} /> : null}
          >
            {updating ? 'Updating...' : 'Update Role'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default UserRoleDialog;
