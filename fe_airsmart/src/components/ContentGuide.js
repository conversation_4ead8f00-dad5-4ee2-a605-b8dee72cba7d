import React from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  IconButton,
  Fade,
  Backdrop,
  useTheme,
  alpha,
  Stack,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon,
  ArrowForward as ArrowForwardIcon,
  ViewInAr as Model3DIcon,
  VideoLibrary as VideoIcon,
  Image as ImageIcon,
  TouchApp as TouchIcon,
  Mouse as MouseIcon,
  ZoomIn as ZoomInIcon,
  CameraAlt as CameraIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  VolumeUp as VolumeIcon,
  Fullscreen as FullscreenIcon,
  PanTool as PanIcon,
  CropFree as FitIcon,
  RadioButtonChecked as HotspotIcon
} from '@mui/icons-material';

const ContentGuide = ({ open, onClose, moduleId }) => {
  const theme = useTheme();

  if (!open) return null;

  return (
    <Backdrop
      open={open}
      sx={{
        zIndex: 9999,
        backgroundColor: alpha(theme.palette.background.default, 0.8),
        backdropFilter: 'blur(8px)'
      }}
    >
      <Fade in={open}>
        <Box
          sx={{
            position: 'fixed',
            top: '50%',
            zIndex: 99999,
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: { xs: '95%', sm: '700px', md: '800px' },
            maxHeight: '90vh',
            overflow: 'auto'
          }}
        >
          <Card
            elevation={0}
            sx={{
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.06)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 3,
              overflow: 'hidden',
              position: 'relative',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                borderColor: theme.palette.primary.main,
                boxShadow: `0 12px 24px ${alpha(theme.palette.primary.main, 0.15)}`,
              }
            }}
          >
            {/* Close Button */}
            <IconButton
              onClick={onClose}
              sx={{
                position: 'absolute',
                top: 16,
                right: 16,
                zIndex: 999999,
                backgroundColor: alpha(theme.palette.background.default, 0.8),
                backdropFilter: 'blur(8px)',
                '&:hover': {
                  backgroundColor: alpha(theme.palette.background.default, 0.9)
                }
              }}
            >
              <CloseIcon />
            </IconButton>

            {/* Header */}
            <Box
              sx={{
                borderBottom: `1px solid ${theme.palette.divider}`,
                p: 4,
                textAlign: 'center',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '2px',
                  backgroundSize: '200% 100%',
                },
                '@keyframes shimmer': {
                  '0%': {
                    backgroundPosition: '-200% 0',
                  },
                  '100%': {
                    backgroundPosition: '200% 0',
                  },
                }
              }}
            >
              <Typography
                variant="h4"
                component="h1"
                gutterBottom
                sx={{
                  fontWeight: 600,
                  color: 'primary.main',
                  mb: 1
                }}
              >
                Quick Guide
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
              >
                Learn how to navigate and interact with content
              </Typography>
         
            </Box>

            {/* Content */}
            <CardContent sx={{ p: 4 }}>
              {/* Interface Guide */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                   Where to find content
                </Typography>
                <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
                  • <strong>Topic titles</strong> are shown in the right panel
                </Typography>
                <Typography variant="body2" sx={{ mb: 3, color: 'text.secondary' }}>
                  • <strong>Topic content</strong> appears when you click on each topic
                </Typography>
              </Box>

              <Divider sx={{ my: 3 }} />

              {/* Content Types Guide */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                   How to interact with content
                </Typography>

                {/* 3D Models */}
                <Box sx={{ mb: 2, p: 2, backgroundColor: alpha(theme.palette.primary.main, 0.05), borderRadius: 1 }}>
                  <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 1 }}>
                    <Model3DIcon sx={{ color: 'primary.main' }} />
                    <Typography variant="subtitle2" fontWeight="600">3D Models</Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                    <TouchIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Click and drag to rotate the model
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                    <MouseIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Scroll mouse wheel to zoom in/out
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                    <HotspotIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Click on highlighted hotspots for information
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <CameraIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Use camera reset button to return to default view
                    </Typography>
                  </Stack>
                </Box>

                {/* Videos */}
                <Box sx={{ mb: 2, p: 2, backgroundColor: alpha(theme.palette.secondary.main, 0.05), borderRadius: 1 }}>
                  <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 1 }}>
                    <VideoIcon sx={{ color: 'secondary.main' }} />
                    <Typography variant="subtitle2" fontWeight="600">Videos</Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                    <PlayIcon sx={{ fontSize: 16, color: 'secondary.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Click play button to start video
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                    <PauseIcon sx={{ fontSize: 16, color: 'secondary.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Use pause/play controls as needed
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                    <VolumeIcon sx={{ fontSize: 16, color: 'secondary.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Adjust volume with volume slider
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <FullscreenIcon sx={{ fontSize: 16, color: 'secondary.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Use fullscreen for better viewing experience
                    </Typography>
                  </Stack>
                </Box>

                {/* Images */}
                <Box sx={{ mb: 2, p: 2, backgroundColor: alpha(theme.palette.success.main, 0.05), borderRadius: 1 }}>
                  <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 1 }}>
                    <ImageIcon sx={{ color: 'success.main' }} />
                    <Typography variant="subtitle2" fontWeight="600">Images</Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                    <ZoomInIcon sx={{ fontSize: 16, color: 'success.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Click on image to zoom in for details
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                    <PanIcon sx={{ fontSize: 16, color: 'success.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Drag zoomed image to pan around
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                    <FitIcon sx={{ fontSize: 16, color: 'success.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Double-click to fit image to screen
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <HotspotIcon sx={{ fontSize: 16, color: 'success.main' }} />
                    <Typography variant="body2" color="text.secondary">
                      Look for numbered annotations (clickable points)
                    </Typography>
                  </Stack>
                </Box>
              </Box>

              <Divider sx={{ my: 3 }} />

              {/* Action Button */}
              <Stack direction="row" justifyContent="center">
                <Button
                  onClick={onClose}
                  endIcon={<ArrowForwardIcon />}
                  variant="contained"
                  sx={{
                    textTransform: 'none',
                    borderRadius: 2,
                    px: 3,
                    py: 1
                  }}
                >
                  Got it
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Box>
      </Fade>
    </Backdrop>
  );
};

export default ContentGuide;
