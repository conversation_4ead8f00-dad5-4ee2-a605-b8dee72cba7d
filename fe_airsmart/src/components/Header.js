import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import LogoutIcon from '@mui/icons-material/Logout';
import HomeIcon from '@mui/icons-material/Home';
import FolderIcon from '@mui/icons-material/Folder';
import PersonIcon from '@mui/icons-material/Person';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Drawer from '@mui/material/Drawer';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Divider from '@mui/material/Divider';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import { auth } from '../firebase';
import { signOut } from 'firebase/auth';
import { useAuth } from '../auth/auth.context';
import { getUserRole, getUserProfile } from '../services/userService';
import airsmartIcon from '../public/icon/airsmart.svg';
import ColorModeSelect from '../page/shared-theme/ColorModeSelect';
import MyFilesDialog from './MyFilesDialog';
import UserRoleDialog from './UserRoleDialog';

// Global cache để tránh gọi getUserRole nhiều lần
let globalUserRoleCache = null;
let globalUserRoleCacheUserId = null;

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: theme.palette.background.default,
  color: theme.palette.text.primary,
  boxShadow: theme.palette.mode === 'dark'
    ? 'hsla(220, 30%, 5%, 0.5) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.08) 0px 15px 35px -5px'
    : 'hsla(220, 30%, 5%, 0.05) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.05) 0px 15px 35px -5px',
}));

const Header = ({ showHomeButton = true }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { currentUser } = useAuth();
  const [filesDialogOpen, setFilesDialogOpen] = useState(false);
  const [userRoleDialogOpen, setUserRoleDialogOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userRole, setUserRole] = useState('installer');
  const [userDisplayName, setUserDisplayName] = useState('');

  // Load user role and profile when component mounts or user changes
  useEffect(() => {
    const loadUserRoleAndProfile = async () => {
      if (currentUser) {
        try {
          // Kiểm tra global cache trước
          if (globalUserRoleCache && globalUserRoleCacheUserId === currentUser.uid) {
            setUserRole(globalUserRoleCache);
          } else {
            const role = await getUserRole();
            // Lưu vào global cache
            globalUserRoleCache = role;
            globalUserRoleCacheUserId = currentUser.uid;
            setUserRole(role);
          }

          const profile = await getUserProfile();
          setUserDisplayName(profile.displayName || 'User');
        } catch (error) {
          console.error('Error loading user role or profile:', error);
          setUserRole('installer'); // Fallback
          setUserDisplayName('User');
        }
      }
    };

    loadUserRoleAndProfile();
  }, [currentUser]);

  const handleLogout = async () => {
    try {
      await signOut(auth);
      window.location.href = '/login';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleHomeClick = () => {
    navigate('/');
  };

  const handleMyFilesClick = () => {
    setFilesDialogOpen(true);
  };

  const handleUserRoleClick = () => {
    setUserRoleDialogOpen(true);
  };

  const handleRoleUpdated = (newRole) => {
    // Clear global cache khi role được update
    globalUserRoleCache = newRole;
    globalUserRoleCacheUserId = currentUser?.uid;
    setUserRole(newRole);
  };

  const handleMobileMenuToggle = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleMobileMenuClose = () => {
    setMobileMenuOpen(false);
  };

  const handleMobileMenuItemClick = (action) => {
    handleMobileMenuClose();
    action();
  };

  return (
    <StyledAppBar position="fixed">
      <Toolbar sx={{ pr: '24px', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <img src={airsmartIcon} alt="AirSmart Icon" style={{ height: 30, width: 30 }} />
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            AirSmart Onboarding Platform
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Desktop Menu */}
          {!isMobile && (
            <>
              {showHomeButton && (
                <IconButton color="inherit" onClick={handleHomeClick} aria-label="home">
                  <HomeIcon />
                </IconButton>
              )}
              <Button
                color="inherit"
                startIcon={<PersonIcon />}
                onClick={handleUserRoleClick}
                sx={{ textTransform: 'none' }}
              >
                {userDisplayName}
              </Button>
              <Button
                color="inherit"
                startIcon={<FolderIcon />}
                onClick={handleMyFilesClick}
                sx={{ textTransform: 'none' }}
              >
                My Files
              </Button>
              <IconButton color="inherit" onClick={handleLogout} aria-label="logout">
                <LogoutIcon />
              </IconButton>
              <ColorModeSelect sx={{ ml: 2 }} />
            </>
          )}

          {/* Mobile Menu Button */}
          {isMobile && (
            <>
              {showHomeButton && (
                <IconButton color="inherit" onClick={handleHomeClick} aria-label="home">
                  <HomeIcon />
                </IconButton>
              )}
              <IconButton
                color="inherit"
                onClick={handleMobileMenuToggle}
                aria-label="menu"
              >
                <MenuIcon />
              </IconButton>
            </>
          )}
        </Box>
      </Toolbar>

      {/* My Files Dialog */}
      <MyFilesDialog
        open={filesDialogOpen}
        onClose={() => setFilesDialogOpen(false)}
        userRole={userRole}
      />

      {/* User Role Dialog */}
      <UserRoleDialog
        open={userRoleDialogOpen}
        onClose={() => setUserRoleDialogOpen(false)}
        onRoleUpdated={handleRoleUpdated}
      />

      {/* Mobile Menu Drawer */}
      <Drawer
        anchor="right"
        open={mobileMenuOpen}
        onClose={handleMobileMenuClose}
        sx={{
          zIndex: 1400,
          '& .MuiDrawer-paper': {
            width: 280,
            backgroundColor: 'background.default',
          }
        }}
      >
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            Menu
          </Typography>
          <IconButton onClick={handleMobileMenuClose}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <List sx={{ pt: 0 }}>
          <ListItem disablePadding>
            <ListItemButton onClick={() => handleMobileMenuItemClick(handleUserRoleClick)}>
              <ListItemIcon>
                <PersonIcon />
              </ListItemIcon>
              <ListItemText
                primary={userDisplayName}
                secondary={userRole.charAt(0).toUpperCase() + userRole.slice(1)}
              />
            </ListItemButton>
          </ListItem>
          <ListItem disablePadding>
            <ListItemButton onClick={() => handleMobileMenuItemClick(handleMyFilesClick)}>
              <ListItemIcon>
                <FolderIcon />
              </ListItemIcon>
              <ListItemText primary="My Files" />
            </ListItemButton>
          </ListItem>
          <Divider sx={{ my: 1 }} />
          <ListItem disablePadding>
            <ListItemButton>
              <ListItemIcon>
                <ColorModeSelect sx={{ minWidth: 'auto', ml: 0.5 }} />
              </ListItemIcon>
              <ListItemText primary="Theme" />
            </ListItemButton>
          </ListItem>
          <Divider sx={{ my: 1 }} />
          <ListItem disablePadding>
            <ListItemButton onClick={() => handleMobileMenuItemClick(handleLogout)}>
              <ListItemIcon>
                <LogoutIcon />
              </ListItemIcon>
              <ListItemText primary="Logout" />
            </ListItemButton>
          </ListItem>
        </List>
      </Drawer>
    </StyledAppBar>
  );
};

export default Header;
