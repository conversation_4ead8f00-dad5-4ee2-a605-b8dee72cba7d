import React from 'react';
import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import Header from './Header';

const MainContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
  '&::before': {
    content: '""',
    display: 'block',
    position: 'absolute',
    zIndex: -1,
    inset: 0,
    backgroundImage: 'radial-gradient(ellipse at 50% 50%, hsl(210, 100%, 97%), hsl(0, 0%, 100%))',
    backgroundRepeat: 'no-repeat',
    ...theme.applyStyles('dark', {
      backgroundImage: 'radial-gradient(at 50% 50%, hsla(210, 100%, 16%, 0.5), hsl(220, 30%, 5%))',
    }),
  },
}));

const MainLayout = ({ children, showHomeButton = true }) => {
  return (
    <MainContainer>
      <Header showHomeButton={showHomeButton} />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          overflow: 'auto',
          pt: { xs: 8, sm: 9 },
          px: { xs: 2, sm: 4 },
          pb: { xs: 4, sm: 6 },
          minHeight: '100vh',
        }}
      >
        {children}
      </Box>
    </MainContainer>
  );
};

export default MainLayout;
