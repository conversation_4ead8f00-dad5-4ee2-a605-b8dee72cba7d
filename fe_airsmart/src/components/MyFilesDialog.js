import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  Close as CloseIcon,
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
  PictureAsPdf as PdfIcon,
  Image as ImageIcon,
  VideoFile as VideoIcon,
  AudioFile as AudioIcon,
  Archive as ArchiveIcon,
  Description as DocumentIcon,
  TableChart as SpreadsheetIcon,
  Slideshow as PresentationIcon,
  Visibility as PreviewIcon,
  Download as DownloadIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { getFilesByRole, getFileType, formatFileSize, canPreviewFile } from '../services/filesService';
import FileViewer from './FileViewer';

const MyFilesDialog = ({ open, onClose, userRole }) => {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileViewerOpen, setFileViewerOpen] = useState(false);

  // Load files when dialog opens
  useEffect(() => {
    if (open && userRole) {
      loadFiles();
    }
  }, [open, userRole]);

  const loadFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await getFilesByRole(userRole);
      setFiles(data.files || []);
    } catch (err) {
      console.error('Error loading files:', err);
      setError('Failed to load files. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Filter files based on search term
  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get file icon based on file type
  const getFileIcon = (fileName) => {
    const fileType = getFileType(fileName);

    const iconMap = {
      pdf: <PdfIcon color="error" />,
      image: <ImageIcon color="primary" />,
      video: <VideoIcon color="secondary" />,
      audio: <AudioIcon color="info" />,
      archive: <ArchiveIcon color="warning" />,
      document: <DocumentIcon color="primary" />,
      spreadsheet: <SpreadsheetIcon color="success" />,
      presentation: <PresentationIcon color="warning" />,
    };

    return iconMap[fileType] || <FileIcon />;
  };

  // Handle file preview
  const handlePreview = (file) => {
    if (canPreviewFile(file.name)) {
      setSelectedFile(file);
      setFileViewerOpen(true);
    }
  };

  // Handle file download
  const handleDownload = (file) => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Format role name for display
  const formatRoleName = (role) => {
    return role.charAt(0).toUpperCase() + role.slice(1);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
      sx={{
        zIndex: 1400
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FolderIcon color="primary" />
            <Typography variant="h6">
              My Files - {formatRoleName(userRole)}
            </Typography>
            <Chip
              label={`${filteredFiles.length} files`}
              size="small"
              color="primary"
              variant="outlined"
            />
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {/* Search Bar */}
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            placeholder="Search files..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            size="small"
          />
        </Box>

        {/* Loading State */}
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {/* Error State */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Files List */}
        {!loading && !error && (
          <>
            {filteredFiles.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <FileIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  {searchTerm ? 'No files found' : 'No files available'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {searchTerm
                    ? 'Try adjusting your search terms'
                    : `No files have been uploaded for ${formatRoleName(userRole)} role yet.`
                  }
                </Typography>
              </Box>
            ) : (
              <List>
                {filteredFiles.map((file, index) => (
                  <React.Fragment key={file.key}>
                    <ListItem>
                      <ListItemIcon>
                        {getFileIcon(file.name)}
                      </ListItemIcon>
                      <ListItemText
                        primary={file.name}
                        secondary={
                          <Box sx={{ display: 'flex', gap: 2, mt: 0.5 }}>
                            <Typography variant="caption" color="text.secondary">
                              {formatFileSize(file.size)}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {new Date(file.lastModified).toLocaleDateString()}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          {canPreviewFile(file.name) && (
                            <IconButton
                              size="small"
                              onClick={() => handlePreview(file)}
                              title="Preview"
                            >
                              <PreviewIcon />
                            </IconButton>
                          )}
                          <IconButton
                            size="small"
                            onClick={() => handleDownload(file)}
                            title="Download"
                          >
                            <DownloadIcon />
                          </IconButton>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < filteredFiles.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Close
        </Button>
        {!loading && !error && filteredFiles.length > 0 && (
          <Button onClick={loadFiles} variant="outlined">
            Refresh
          </Button>
        )}
      </DialogActions>

      {/* File Viewer Dialog */}
      <FileViewer
        open={fileViewerOpen}
        onClose={() => setFileViewerOpen(false)}
        file={selectedFile}
      />
    </Dialog>
  );
};

export default MyFilesDialog;
