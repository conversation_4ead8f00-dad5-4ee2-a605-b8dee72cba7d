import * as React from 'react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Box from '@mui/material/Box';
import {
  Container,
  CircularProgress,
  Snackbar,
  Alert,
  Button,
  Typography,
  Paper,
  Fade,
  useTheme,
  alpha,
  Avatar,
  Stack
} from '@mui/material';
import {
  PlayArrow as PlayArrowIcon,
  AutoStories as StartIcon
} from '@mui/icons-material';
import { keyframes } from '@mui/system';
import airsmartIcon from '../../public/icon/airsmart.svg';
import ModernModuleGrid from './components/ModernModuleGrid';
import { useAuth } from '../../auth/auth.context';
import { useUserProgress } from '../../hooks/useUserProgress';
import { fetchCoursesWithModules } from '../../services/moduleService';

import MainLayout from '../../components/MainLayout';

// Define animations
const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.8;
  }
`;

const glow = keyframes`
  0% {
    box-shadow: 0 0 20px rgba(25, 118, 210, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(25, 118, 210, 0.6);
  }
  100% {
    box-shadow: 0 0 20px rgba(25, 118, 210, 0.3);
  }
`;

export default function Homepage() {
  const [showSplash, setShowSplash] = React.useState(true);
  const [logoLoaded, setLogoLoaded] = React.useState(false);
  const [showWelcome, setShowWelcome] = React.useState(false);
  const [allModules, setAllModules] = React.useState([]);

  const { currentUser, loading } = useAuth(); // Lấy loading từ AuthProvider
  const navigate = useNavigate();
  const {
    loading: progressLoading,
    userProgress,
    isModuleCompleted
  } = useUserProgress();
  const theme = useTheme();

  // Fetch all modules khi mount
  React.useEffect(() => {
    let mounted = true;
    fetchCoursesWithModules(currentUser?.uid).then((data) => {
      if (!mounted) return;
      // Flatten modules từ container và standalone
      const flatModules = [];
      data.forEach(item => {
        if (item.isContainer && item.modules) {
          flatModules.push(...item.modules);
        } else if (!item.isContainer) {
          flatModules.push(item);
        }
      });
      setAllModules(flatModules);
    });
    return () => { mounted = false; };
  }, [currentUser?.uid]);



  // Determine which button to show and navigation logic
  const getButtonAction = () => {
    if (!currentUser || progressLoading || allModules.length === 0) {
      return null;
    }

    // Tìm module đầu tiên chưa hoàn thành (theo thứ tự)
    const firstUncompleted = allModules.find(module => !isModuleCompleted(module.id));
    const hasAnyProgress = Object.keys(userProgress).length > 0;
    if (firstUncompleted) {
      // Nếu user chưa học gì (userProgress rỗng) thì là Start Learning, còn lại luôn là Continue Learning
      return {
        type: hasAnyProgress ? 'continue' : 'start',
        label: hasAnyProgress ? 'Continue Learning' : 'Start Learning',
        moduleId: firstUncompleted.id,
        icon: hasAnyProgress ? <PlayArrowIcon /> : <StartIcon />
      };
    }

    // Nếu đã hoàn thành hết, có thể cho học lại từ đầu hoặc ẩn nút
    // return null; // Ẩn nút
    // Hoặc cho học lại từ đầu:
    return {
      type: 'start',
      label: 'Start Again',
      moduleId: allModules[0]?.id,
      icon: <StartIcon />
    };
  };

  const handleButtonClick = () => {
    const action = getButtonAction();
    if (action) {
      navigate(`/module/${action.moduleId}`);
    }
  };

  // Chỉ refresh data khi cần thiết (khi user thay đổi)
  useEffect(() => {
    if (currentUser) {
      // Chỉ refresh khi user thay đổi, không phải mỗi lần mount
      window.dispatchEvent(new CustomEvent('refreshUserProgress'));
    }
  }, [currentUser]); // Chỉ phụ thuộc vào currentUser

  // Show welcome message for new users
  useEffect(() => {
    if (currentUser) {
      // Listen for user initialization events
      const handleUserInitialized = (event) => {
        const { isNewUser } = event.detail;
        if (isNewUser) {
          setShowWelcome(true);
        }
      };

      window.addEventListener('userInitialized', handleUserInitialized);

      return () => {
        window.removeEventListener('userInitialized', handleUserInitialized);
      };
    }
  }, [currentUser]);

  React.useEffect(() => {
    let timer;
    if (logoLoaded) {
      // Shorter splash screen time (1.5 seconds instead of 2)
      timer = setTimeout(() => setShowSplash(false), 1500);
    }
    return () => clearTimeout(timer);
  }, [logoLoaded]);

  // Ensure splash screen doesn't show for too long even if logo doesn't load
  React.useEffect(() => {
    const maxSplashTimer = setTimeout(() => {
      setShowSplash(false);
      setLogoLoaded(true);
    }, 3000); // Maximum 3 seconds for splash screen

    return () => clearTimeout(maxSplashTimer);
  }, []);

  const handleLogoLoad = () => setLogoLoaded(true);

  const buttonAction = getButtonAction();

  // Nếu AuthProvider còn loading thì không render gì (đã có splash ở AuthProvider)

  return (
    <MainLayout showHomeButton={false}>
      {loading ?  (
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: '100vh',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg, ${theme.palette.primary.main}10 0%, ${theme.palette.primary.light}05 100%)`, // đồng bộ màu với AuthProvider
          backdropFilter: 'blur(5px)',
          zIndex: 9999
        }}>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            animation: `${fadeIn} 1s ease-in-out`
          }}>
            <img
              src={airsmartIcon}
              alt="AirSmart Logo"
              style={{
                width: 80,
                height: 80,
                marginBottom: 24,
                animation: `${pulse} 1.5s infinite ease-in-out`
              }}
              onLoad={handleLogoLoad}
            />
            <CircularProgress
              color="primary"
              size={40}
              thickness={4}
              sx={{
                opacity: 0.8
              }}
            />
          </Box>
        </Box>
      ) : (
        <Container maxWidth="xl" sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 'calc(100vh - 120px)'
        }}>
          {/* Enhanced Quick Action Button */}
          {buttonAction && (
            <Fade in={true} timeout={800}>
              <Paper
                elevation={0}
                sx={{
                  p: { xs: 2, sm: 3 },
                  mb: 3,
                  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.06)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 3,
                  textAlign: 'center',
                  minWidth: { xs: 240, sm: 320 },
                  maxWidth: 400,
                  position: 'relative',
                  overflow: 'hidden',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-4px) scale(1.01)',
                    borderColor: theme.palette.primary.main,
                    boxShadow: `0 12px 24px ${alpha(theme.palette.primary.main, 0.15)}`,
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '2px',
                    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 50%, ${theme.palette.primary.main} 100%)`,
                    backgroundSize: '200% 100%',
                    animation: 'shimmer 2s linear infinite',
                  },
                  '@keyframes shimmer': {
                    '0%': {
                      backgroundPosition: '-200% 0',
                    },
                    '100%': {
                      backgroundPosition: '200% 0',
                    },
                  }
                }}
              >
                <Stack direction="column" alignItems="center" spacing={1.5}>
                  {/* <Avatar
                    sx={{
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      color: theme.palette.primary.main,
                      width: { xs: 48, sm: 56 },
                      height: { xs: 48, sm: 56 },
                      border: '2px solid',
                      borderColor: alpha(theme.palette.primary.main, 0.2),
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'scale(1.05) rotate(3deg)',
                        borderColor: theme.palette.primary.main,
                      }
                    }}
                  >
                    {buttonAction.icon}
                  </Avatar> */}

                  <Box>
                    <Typography
                      variant="h6"
                      component="h2"
                      gutterBottom
                      sx={{
                        fontWeight: 600,
                        fontSize: { xs: '1.1rem', sm: '1.25rem' },
                        color: theme.palette.mode === 'dark'
                          ? theme.palette.primary.light
                          : theme.palette.primary.main,
                        mb: 0.5
                      }}
                    >
                      {buttonAction.type === 'continue' ? 'Continue Learning' : 'Start Learning'}
                    </Typography>

                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        mb: 2,
                        lineHeight: 1.5,
                        fontSize: { xs: '0.8rem', sm: '0.875rem' },
                        fontWeight: 500
                      }}
                    >
                      {buttonAction.type === 'continue'
                        ? 'Pick up where you left off'
                        : 'Begin your learning journey'
                      }
                    </Typography>
                  </Box>

                  <Button
                    variant="contained"
                    size="medium"
                    startIcon={buttonAction.icon}
                    onClick={handleButtonClick}
                    sx={{
                      bgcolor: theme.palette.primary.main,
                      color: 'white',
                      fontWeight: 600,
                      fontSize: { xs: '0.85rem', sm: '0.9rem' },
                      px: { xs: 2.5, sm: 3 },
                      py: { xs: 1, sm: 1.2 },
                      borderRadius: 2,
                      textTransform: 'none',
                      minWidth: { xs: 140, sm: 160 },
                      boxShadow: `0 6px 16px ${alpha(theme.palette.primary.main, 0.25)}`,
                      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        transform: 'translateY(-2px) scale(1.03)',
                        boxShadow: `0 8px 20px ${alpha(theme.palette.primary.main, 0.3)}`,
                        background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
                      },
                      '&:active': {
                        transform: 'translateY(-1px) scale(1.01)',
                      }
                    }}
                  >
                    {buttonAction.label}
                  </Button>
                </Stack>
              </Paper>
            </Fade>
          )}

          {/* Module Grid */}
          <ModernModuleGrid />
        </Container>
      )}

      {/* Welcome Snackbar for new users */}
      <Snackbar
        open={showWelcome}
        autoHideDuration={6000}
        onClose={() => setShowWelcome(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowWelcome(false)}
          severity="success"
          sx={{ width: '100%' }}
        >
          Welcome! Your profile has been created with default role: Installer.
          You can change your role anytime by clicking the Role button in the header.
        </Alert>
      </Snackbar>
    </MainLayout>
  );
}