import React, { useState } from 'react';
import { Html } from '@react-three/drei';
import { useMediaQuery } from '@mui/material';

const Hotspot = React.memo(function Hotspot({
  hotspot,
  index,
  onHotspotClick
}) {
  const [showTooltip, setShowTooltip] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Mobile detection
  const mobile = {
    isMobile: useMediaQuery('(max-width:768px)')
  };

  const handleClick = (e) => {
    e.stopPropagation();
    setShowTooltip(!showTooltip);

    if (onHotspotClick) {
      onHotspotClick(hotspot);
    }

    if (hotspot.onClick) {
      hotspot.onClick();
    }
  };

  const handleClose = (e) => {
    e.stopPropagation();
    setShowTooltip(false);
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // Keep original hotspot color (blue)
  const hotspotColor = "#1976d2";

  return (
    <mesh
      position={hotspot.position || [0, 0, 0]}
      onClick={handleClick}
      onPointerEnter={handleMouseEnter}
      onPointerLeave={handleMouseLeave}
    >
      <sphereGeometry args={[0.1, 16, 16]} />
      <meshBasicMaterial
        color={isHovered ? "#1976d2" : hotspotColor}
        transparent={true}
        opacity={isHovered ? 0.9 : 0.8}
      />

      {/* Hotspot Label - Keep original style but make clickable */}
      <Html
        distanceFactor={10}
        style={{
          cursor: 'pointer',
          pointerEvents: 'auto'
        }}
      >
        <div
          onClick={handleClick}
          style={{
            background: 'rgba(25, 118, 210, 0.9)',
            color: 'white',
            padding: '3px 6px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: 'bold',
            pointerEvents: 'auto',
            cursor: 'pointer',
            whiteSpace: 'nowrap',
            boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
            border: '1px solid rgba(255,255,255,0.2)'
          }}
        >
          {hotspot.label || hotspot.id || `H${index + 1}`}
        </div>
      </Html>

      {/* Description Tooltip */}
      {showTooltip && hotspot.description && (
        <Html
          position={[0, 0.3, 0]}
          distanceFactor={8}
          occlude
          style={{
            pointerEvents: 'auto',
            zIndex: 1000
          }}
        >
          <div
            style={{
              background: "rgba(0, 0, 0, 0.95)",
              color: "white",
              padding: mobile.isMobile ? "10px" : "7px",
              borderRadius: mobile.isMobile ? "12px" : "10px",
              minWidth: mobile.isMobile ? "280px" : "270px",
              maxWidth: mobile.isMobile ? "350px" : "330px",
              width: "auto",
              height: "auto",
              minHeight: mobile.isMobile ? "100px" : "90px",
              maxHeight: mobile.isMobile ? "220px" : "200px",
              boxShadow: "0 6px 24px rgba(0, 0, 0, 0.7)",
              border: "1px solid rgba(255, 255, 255, 0.3)",
              position: "relative",
              zIndex: 1000,
              overflow: "hidden",
              wordWrap: "break-word",
              backdropFilter: "blur(8px)",
              fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            }}
          >
            {/* Close button */}
            <button
              onClick={handleClose}
              onTouchStart={(e) => {
                e.stopPropagation();
              }}
              onTouchEnd={handleClose}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(255, 255, 255, 0.4)';
                e.target.style.transform = 'scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'rgba(255, 255, 255, 0.25)';
                e.target.style.transform = 'scale(1)';
              }}
              style={{
                position: "absolute",
                top: "8px",
                right: "8px",
                background: "rgba(255, 255, 255, 0.25)",
                border: "1px solid rgba(255, 255, 255, 0.3)",
                color: "white",
                width: mobile.isMobile ? "32px" : "20px",
                height: mobile.isMobile ? "32px" : "20px",
                borderRadius: "50%",
                cursor: "pointer",
                fontSize: mobile.isMobile ? "18px" : "12px",
                fontWeight: "bold",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                zIndex: 10000,
                touchAction: "manipulation",
                transition: "all 0.2s ease",
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.3)",
              }}
            >
              ×
            </button>

            {/* Title */}
            <h3 style={{
              margin: "0 0 5px 0",
              fontSize: mobile.isMobile ? "18px" : "15px",
              fontWeight: "700",
              color: "#fff",
              textShadow: "0 1px 2px rgba(0,0,0,0.8)",
              letterSpacing: "0.3px"
            }}>
              {hotspot.label || hotspot.id || `Hotspot ${index + 1}`}
            </h3>

            {/* Description */}
            <p style={{
              fontSize: mobile.isMobile ? "15px" : "12px",
              lineHeight: "1.2",
              margin: "0 0 14px 0",
              opacity: 0.95,
              wordWrap: "break-word",
              overflowWrap: "break-word",
              hyphens: "auto",
              textShadow: "0 1px 1px rgba(0,0,0,0.6)",
              letterSpacing: "0.2px"
            }}>
              {hotspot.description}
            </p>
          </div>
        </Html>
      )}
    </mesh>
  );
});

export default Hotspot;