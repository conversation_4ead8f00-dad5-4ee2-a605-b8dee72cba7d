import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  Checkbox,
  CircularProgress,
  Alert,

  Card,
  CardContent,
  Divider,
  LinearProgress,
  Snackbar
} from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';

import MainLayout from '../../components/MainLayout';
import { useAuth } from '../../auth/auth.context';
import { useUserProgress } from '../../hooks/useUserProgress';
import { getModule, getNextUncompletedModule } from '../../services/moduleService';
import { getQuizQuestions, getQuizPassingScore, getQuizAnswers } from '../../services/quizService';
import { updateModuleProgress } from '../../services/userProgressService';

function ModuleQuiz() {
  const { moduleId } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { isModuleCompleted } = useUserProgress();

  // State variables
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [module, setModule] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [score, setScore] = useState(0);
  const [passingScore, setPassingScore] = useState(70); // Default to 70
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [quizAnswers, setQuizAnswers] = useState([]);

  // Sử dụng ref để theo dõi xem đã tải dữ liệu chưa
  const dataLoadedRef = useRef(false);

  // Load module and quiz questions
  useEffect(() => {
    // Nếu đã tải dữ liệu rồi, không tải lại
    if (dataLoadedRef.current) {
      return;
    }

    const loadQuizData = async () => {
      try {
        setLoading(true);
        setError(null);
        // Check if user is logged in
        if (!currentUser) {
          console.error('User not logged in');
          setError('You must be logged in to take a quiz');
          setLoading(false);
          return;
        }

        // Get module data
        const moduleData = await getModule(moduleId);
        if (!moduleData) {
          console.error(`Module ${moduleId} not found`);
          setError('Module not found');
          setLoading(false);
          return;
        }
        setModule(moduleData);

        // Check if module is already completed - gọi function bên trong effect
        const completed = isModuleCompleted(moduleId);
        if (completed) {
          setSnackbarMessage('You have already completed this module');
          setSnackbarOpen(true);
        }

        // Get quiz questions
        const quizData = await getQuizQuestions(moduleId);

        if (!quizData || quizData.length === 0) {
          console.error(`No quiz questions found for module ${moduleId}`);
          setError('No quiz questions found for this module');
          setLoading(false);
          return;
        }

        // Validate quiz data structure
        const isValidQuizData = quizData.every(q =>
          q.id && q.text && Array.isArray(q.options) &&
          q.options.length > 0 && q.options.every(o => o.id && o.text !== undefined)
        );

        if (!isValidQuizData) {
          console.error('Invalid quiz data structure:', quizData);
          setError('Quiz data is in an invalid format. Please contact support.');
          setLoading(false);
          return;
        }

        setQuestions(quizData);

        // Get passing score for this module
        const modulePassingScore = await getQuizPassingScore(moduleId);
        setPassingScore(modulePassingScore);

        // Initialize answers object
        const initialAnswers = {};
        quizData.forEach(question => {
          initialAnswers[question.id] = question.type === 'multiple' ? [] : '';
        });
        setAnswers(initialAnswers);
        setLoading(false);
        // Đánh dấu đã tải dữ liệu
        dataLoadedRef.current = true;
      } catch (err) {
        console.error('Error loading quiz data:', err);
        setError(`Failed to load quiz data: ${err.message || 'Unknown error'}`);
        setLoading(false);
      }
    };

    loadQuizData();
  }, [moduleId, currentUser]); // ✅ Removed isModuleCompleted from dependencies

  // Handle answer changes
  const handleAnswerChange = (questionId, value) => {
    const question = questions.find(q => q.id === questionId);

    if (question.type === 'multiple') {
      setAnswers(prev => {
        const currentAnswers = [...prev[questionId]];

        if (currentAnswers.includes(value)) {
          // Remove if already selected
          return {
            ...prev,
            [questionId]: currentAnswers.filter(answer => answer !== value)
          };
        } else {
          // Add if not selected
          return {
            ...prev,
            [questionId]: [...currentAnswers, value]
          };
        }
      });
    } else {
      // Single choice question
      setAnswers(prev => ({
        ...prev,
        [questionId]: value
      }));
    }
  };

  // Handle quiz submission
  const handleSubmit = async () => {
    // Calculate score
    let correctAnswers = 0;
    let totalQuestions = questions.length;

    // Lấy đáp án đúng và giải thích từ backend
    let answersFromServer = [];
    try {
      answersFromServer = await getQuizAnswers(moduleId);
      setQuizAnswers(answersFromServer);
    } catch (err) {
      console.error('Error getting quiz answers:', err);
      setQuizAnswers([]);
    }

    questions.forEach(question => {
      const userAnswer = answers[question.id];
      // Tìm đáp án đúng từ server
      const serverAnswer = answersFromServer.find(a => a.id === question.id);
      let correctAnswerIndexes = Array.isArray(serverAnswer?.correctAnswer) ? serverAnswer.correctAnswer : [];
      if (question.type === 'multiple') {
        // For multiple choice, all correct options must be selected and no incorrect ones
        const correctOptions = correctAnswerIndexes.map(idx => question.options[idx]?.id);
        const isCorrect =
          userAnswer.length === correctOptions.length &&
          correctOptions.every(optionId => userAnswer.includes(optionId));
        if (isCorrect) correctAnswers++;
      } else {
        // For single choice
        const correctOptionId = question.options[correctAnswerIndexes[0]]?.id;
        if (correctOptionId && userAnswer === correctOptionId) {
          correctAnswers++;
        }
      }
    });

    const finalScore = Math.round((correctAnswers / totalQuestions) * 100);
    setScore(finalScore);
    setSubmitted(true);

    // Update user progress
    try {
      await updateModuleProgress(
        currentUser.uid,
        moduleId,
        '',
        false,
        finalScore
      );
      const passed = finalScore >= passingScore;
      setSnackbarMessage(
        passed
          ? `Quiz submitted! Your score: ${finalScore}%. Module completed!`
          : `Quiz submitted! Your score: ${finalScore}%. You need ${passingScore}% to pass. Please try again.`
      );
      setSnackbarOpen(true);
    } catch (err) {
      setSnackbarMessage('Error saving your progress');
      setSnackbarOpen(true);
    }
  };

  // Navigation handlers
  const handleNext = () => {
    setCurrentQuestionIndex(prev => Math.min(prev + 1, questions.length - 1));
  };

  const handleBack = () => {
    setCurrentQuestionIndex(prev => Math.max(prev - 1, 0));
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Handle continue to next module - updated logic
  const handleContinueToNextModule = async () => {
    try {
      // Get the next uncompleted module
      const nextModuleData = await getNextUncompletedModule(moduleId, isModuleCompleted);
      
      if (nextModuleData) {
        navigate(`/module/${nextModuleData.id}`);
      } else {
        // No next module, go to homepage
        navigate('/');
      }
    } catch (error) {
      console.error('Error getting next module:', error);
      // Fallback to homepage
      navigate('/');
    }
  };

  // Loading state
  if (loading) {
    return (
      <MainLayout>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
          <CircularProgress />
        </Box>
      </MainLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <MainLayout>
        <Box sx={{ p: 3 }}>
          <Alert severity="error">{error}</Alert>
          <Button variant="contained" onClick={() => navigate(`/module/${moduleId}`)} sx={{ mt: 2, mr: 2 }}>
            Back to Module
          </Button>
          <Button variant="outlined" onClick={() => navigate('/')} sx={{ mt: 2 }}>
            Back to Home
          </Button>
        </Box>
      </MainLayout>
    );
  }

  // Results view after submission
  if (submitted) {
    const passed = score >= passingScore;
    
    return (
      <MainLayout>
        <Container maxWidth="md" sx={{ py: 4 }}>
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <Box sx={{ mb: 4 }}>
              <EmojiEventsIcon sx={{ fontSize: 80, color: passed ? 'success.main' : 'warning.main' }} />
              <Typography variant="h4" gutterBottom>
                Quiz Results
              </Typography>
              <Typography variant="h5" color={passed ? 'success.main' : 'warning.main'}>
                Your Score: {score}%
              </Typography>
              <Typography variant="body1" sx={{ mt: 2 }}>
                {passed
                  ? 'Congratulations! You have successfully completed this module.'
                  : `You need to score at least ${passingScore}% to pass. Please try again.`}
              </Typography>
            </Box>

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom align="left">
              Question Summary:
            </Typography>

            {questions.map((question, index) => {
              const userAnswer = answers[question.id];
              // Tìm đáp án đúng và giải thích từ server
              const serverAnswer = quizAnswers.find(a => a.id === question.id);
              let correctAnswerIndexes = Array.isArray(serverAnswer?.correctAnswer) ? serverAnswer.correctAnswer : [];
              let explanation = serverAnswer?.explanation || '';
              let isCorrect = false;
              if (question.type === 'multiple') {
                const correctOptions = correctAnswerIndexes.map(idx => question.options[idx]?.id);
                isCorrect =
                  userAnswer.length === correctOptions.length &&
                  correctOptions.every(optionId => userAnswer.includes(optionId));
              } else {
                const correctOptionId = question.options[correctAnswerIndexes[0]]?.id;
                isCorrect = correctOptionId && userAnswer === correctOptionId;
              }
              return (
                <Card key={question.id} sx={{ mb: 2, bgcolor: isCorrect ? 'success.50' : 'error.50' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                      {isCorrect
                        ? <CheckCircleOutlineIcon color="success" />
                        : <ErrorOutlineIcon color="error" />}
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle1" sx={{ textAlign: 'left' }}>
                          Question {index + 1}: {question.text}
                        </Typography>
                        <Box sx={{ textAlign: 'center', mt: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            {isCorrect ? 'Correct' : 'Incorrect'}
                          </Typography>
                        </Box>
                        {explanation && (
                          <Box sx={{ mt: 2, textAlign: 'left' }}>
                            <Typography variant="body2" color="info.main">
                              Explanation: {explanation}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              );
            })}

            <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center', gap: 2 }}>
              <Button
                variant="outlined"
                onClick={() => navigate(`/module/${moduleId}`)}
                startIcon={<ArrowBackIcon />}
              >
                Back to Module
              </Button>

              {passed ? (
                <Button
                  variant="contained"
                  color="success"
                  onClick={handleContinueToNextModule}
                  endIcon={<ArrowForwardIcon />}
                >
                  Next Module
                </Button>
              ) : (
                <Button
                  variant="contained"
                  color="warning"
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </Button>
              )}
            </Box>
          </Paper>
        </Container>
      </MainLayout>
    );
  }

  // Quiz view
  const currentQuestion = questions[currentQuestionIndex];

  return (
    <MainLayout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Module Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" gutterBottom>
              {module?.name} - Quiz
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Complete this quiz to finish the module
            </Typography>
          </Box>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate(`/module/${moduleId}`)}
          >
            Back to Module
          </Button>
        </Box>

      {/* Progress indicator */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2">
            Question {currentQuestionIndex + 1} of {questions.length}
          </Typography>
          <Typography variant="body2">
            {Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}% Complete
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={((currentQuestionIndex + 1) / questions.length) * 100}
        />
      </Box>

      {/* Question card */}
      <Paper sx={{ p: 4, mb: 4 }}>
        {currentQuestion && (
          <>
            <Typography variant="h6" gutterBottom>
              Question {currentQuestionIndex + 1}: {currentQuestion.text}
            </Typography>

            <FormControl component="fieldset" sx={{ mt: 3, width: '100%' }}>
              <FormLabel component="legend">
                {currentQuestion.type === 'multiple'
                  ? 'Select all that apply'
                  : 'Select one answer'}
              </FormLabel>

              {currentQuestion.type === 'multiple' ? (
                <Box sx={{ mt: 2 }}>
                  {currentQuestion.options.map(option => (
                    <FormControlLabel
                      key={option.id}
                      control={
                        <Checkbox
                          checked={answers[currentQuestion.id].includes(option.id)}
                          onChange={() => handleAnswerChange(currentQuestion.id, option.id)}
                        />
                      }
                      label={option.text}
                      sx={{ display: 'block', mb: 1 }}
                    />
                  ))}
                </Box>
              ) : (
                <RadioGroup
                  value={answers[currentQuestion.id]}
                  onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                  sx={{ mt: 2 }}
                >
                  {currentQuestion.options.map(option => (
                    <FormControlLabel
                      key={option.id}
                      value={option.id}
                      control={<Radio />}
                      label={option.text}
                      sx={{ mb: 1 }}
                    />
                  ))}
                </RadioGroup>
              )}
            </FormControl>
          </>
        )}
      </Paper>

      {/* Navigation buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Button
          variant="outlined"
          onClick={handleBack}
          disabled={currentQuestionIndex === 0}
          startIcon={<ArrowBackIcon />}
        >
          Previous
        </Button>

        <Box>
          {currentQuestionIndex === questions.length - 1 ? (
            <Button
              variant="contained"
              color="success"
              onClick={handleSubmit}
              disabled={Object.values(answers).some(answer =>
                (Array.isArray(answer) && answer.length === 0) ||
                (!Array.isArray(answer) && !answer)
              )}
            >
              Submit Quiz
            </Button>
          ) : (
            <Button
              variant="contained"
              onClick={handleNext}
              endIcon={<ArrowForwardIcon />}
            >
              Next
            </Button>
          )}
        </Box>
      </Box>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={5000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Container>
    </MainLayout>
  );
}

export default ModuleQuiz;
