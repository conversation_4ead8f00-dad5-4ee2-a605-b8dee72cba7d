import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Container,
  Paper,
  Divider
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { verifyResetToken, resetPassword } from '../../auth/passwordReset.service';
import LockResetIcon from '@mui/icons-material/LockReset';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';

const StyledCard = styled(Card)(({ theme }) => ({
  maxWidth: 450,
  margin: '0 auto',
  marginTop: theme.spacing(8),
  padding: theme.spacing(3),
  borderRadius: theme.spacing(2),
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
}));

const ResetPasswordPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const token = searchParams.get('token');

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [tokenValid, setTokenValid] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');

  useEffect(() => {
    if (!token) {
      setError('Invalid reset link. Please request a new password reset.');
      setVerifying(false);
      return;
    }

    const verifyToken = async () => {
      try {
        const result = await verifyResetToken(token);
        if (result.valid) {
          setTokenValid(true);
        } else {
          setError('This reset link has expired or is invalid. Please request a new password reset.');
        }
      } catch (err) {
        setError('Failed to verify reset link. Please try again or request a new password reset.');
      } finally {
        setVerifying(false);
      }
    };

    verifyToken();
  }, [token]);

  const validatePassword = (password) => {
    if (password.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    return '';
  };

  const handlePasswordChange = (e) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    setPasswordError(validatePassword(newPassword));
    
    if (confirmPassword && newPassword !== confirmPassword) {
      setConfirmPasswordError('Passwords do not match');
    } else {
      setConfirmPasswordError('');
    }
  };

  const handleConfirmPasswordChange = (e) => {
    const newConfirmPassword = e.target.value;
    setConfirmPassword(newConfirmPassword);
    
    if (password !== newConfirmPassword) {
      setConfirmPasswordError('Passwords do not match');
    } else {
      setConfirmPasswordError('');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Reset error states
    setError('');
    const passwordValidationError = validatePassword(password);
    
    if (passwordValidationError) {
      setPasswordError(passwordValidationError);
      return;
    }
    
    if (password !== confirmPassword) {
      setConfirmPasswordError('Passwords do not match');
      return;
    }

    try {
      setLoading(true);
      await resetPassword(token, password);
      setSuccess(true);
    } catch (err) {
      setError(err.message || 'Failed to reset password. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  if (verifying) {
    return (
      <Container maxWidth="sm">
        <StyledCard>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <CircularProgress size={48} sx={{ mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Verifying Reset Link...
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Please wait while we verify your password reset link.
            </Typography>
          </CardContent>
        </StyledCard>
      </Container>
    );
  }

  if (!tokenValid && !success) {
    return (
      <Container maxWidth="sm">
        <StyledCard>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <ErrorIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom color="error">
              Invalid Reset Link
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              {error || 'This password reset link is invalid or has expired.'}
            </Typography>
            <Button 
              variant="contained" 
              onClick={handleBackToLogin}
              fullWidth
            >
              Back to Login
            </Button>
          </CardContent>
        </StyledCard>
      </Container>
    );
  }

  if (success) {
    return (
      <Container maxWidth="sm">
        <StyledCard>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <CheckCircleIcon sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom color="success.main">
              Password Reset Successful!
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Your password has been successfully reset. You can now sign in with your new password.
            </Typography>
            <Button 
              variant="contained" 
              onClick={handleBackToLogin}
              fullWidth
            >
              Sign In Now
            </Button>
          </CardContent>
        </StyledCard>
      </Container>
    );
  }

  return (
    <Container maxWidth="sm">
      <StyledCard>
        <CardContent>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Paper
              elevation={0}
              sx={{
                width: 64,
                height: 64,
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: 'primary.main',
                color: 'primary.contrastText',
                margin: '0 auto',
                mb: 2
              }}
            >
              <LockResetIcon sx={{ fontSize: 32 }} />
            </Paper>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Reset Your Password
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Enter your new password below
            </Typography>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="New Password"
              type="password"
              value={password}
              onChange={handlePasswordChange}
              error={!!passwordError}
              helperText={passwordError || 'Minimum 6 characters required'}
              margin="normal"
              required
              disabled={loading}
              autoFocus
            />

            <TextField
              fullWidth
              label="Confirm New Password"
              type="password"
              value={confirmPassword}
              onChange={handleConfirmPasswordChange}
              error={!!confirmPasswordError}
              helperText={confirmPasswordError || 'Re-enter your new password'}
              margin="normal"
              required
              disabled={loading}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading || !!passwordError || !!confirmPasswordError || !password || !confirmPassword}
              sx={{ mt: 3, mb: 2, py: 1.5 }}
              startIcon={loading ? <CircularProgress size={16} /> : null}
            >
              {loading ? 'Resetting Password...' : 'Reset Password'}
            </Button>

            <Button
              fullWidth
              variant="text"
              onClick={handleBackToLogin}
              disabled={loading}
            >
              Back to Sign In
            </Button>
          </Box>
        </CardContent>
      </StyledCard>
    </Container>
  );
};

export default ResetPasswordPage;