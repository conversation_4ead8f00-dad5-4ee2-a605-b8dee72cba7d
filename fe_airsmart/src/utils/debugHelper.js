/**
 * Debug helper đ<PERSON> kiểm tra và monitor API calls
 */

import { getCacheStats } from '../services/apiService';

class DebugHelper {
  constructor() {
    this.apiCallCount = 0;
    this.apiCalls = [];
    this.startTime = Date.now();
  }

  /**
   * Log API call
   * @param {string} method - HTTP method
   * @param {string} url - API URL
   * @param {string} type - Type of call (cache hit, cache miss, etc.)
   */
  logApiCall(method, url, type = 'normal') {
    this.apiCallCount++;
    const call = {
      id: this.apiCallCount,
      method,
      url,
      type,
      timestamp: Date.now(),
      timeFromStart: Date.now() - this.startTime
    };
    
    this.apiCalls.push(call);
    
    // Chỉ giữ lại 100 calls gần nhất
    if (this.apiCalls.length > 100) {
      this.apiCalls = this.apiCalls.slice(-100);
    }
    
  }

  /**
   * <PERSON><PERSON><PERSON> thống kê <PERSON> calls
   * @returns {Object} Thống kê
   */
  getStats() {
    const now = Date.now();
    const totalTime = now - this.startTime;
    
    // <PERSON><PERSON><PERSON> calls
    const callTypes = this.apiCalls.reduce((acc, call) => {
      acc[call.type] = (acc[call.type] || 0) + 1;
      return acc;
    }, {});
    
    // Đếm calls theo endpoint
    const endpointCounts = this.apiCalls.reduce((acc, call) => {
      const endpoint = call.url.split('?')[0]; // Remove query params
      acc[endpoint] = (acc[endpoint] || 0) + 1;
      return acc;
    }, {});
    
    // Tìm endpoints được gọi nhiều nhất
    const topEndpoints = Object.entries(endpointCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);
    
    return {
      totalCalls: this.apiCallCount,
      totalTime: totalTime,
      callsPerMinute: (this.apiCallCount / (totalTime / 60000)).toFixed(2),
      callTypes,
      topEndpoints,
      recentCalls: this.apiCalls.slice(-10),
      cacheStats: getCacheStats()
    };
  }

  /**
   * Reset stats
   */
  reset() {
    this.apiCallCount = 0;
    this.apiCalls = [];
    this.startTime = Date.now();
  }

  /**
   * Kiểm tra xem có API calls bị duplicate không
   * @param {number} timeWindow - Thời gian window để kiểm tra (ms)
   * @returns {Array} Danh sách duplicate calls
   */
  findDuplicateCalls(timeWindow = 1000) {
    const duplicates = [];
    const callMap = new Map();
    
    this.apiCalls.forEach(call => {
      const key = `${call.method}_${call.url}`;
      const existing = callMap.get(key);
      
      if (existing && call.timestamp - existing.timestamp < timeWindow) {
        duplicates.push({
          original: existing,
          duplicate: call,
          timeDiff: call.timestamp - existing.timestamp
        });
      }
      
      callMap.set(key, call);
    });
    
    return duplicates;
  }

  /**
   * Tìm các API calls có thể được cache
   * @returns {Array} Danh sách calls có thể cache
   */
  findCacheableCalls() {
    const cacheableCalls = this.apiCalls.filter(call => 
      call.method === 'GET' && 
      call.type === 'normal' &&
      (call.url.includes('/module') || call.url.includes('/quiz'))
    );
    
    return cacheableCalls;
  }
}

// Tạo instance global
const debugHelper = new DebugHelper();

// Expose to window for debugging
if (typeof window !== 'undefined') {
  window.debugHelper = debugHelper;
  window.printApiStats = () => debugHelper.printStats();
  window.resetApiStats = () => debugHelper.reset();
  window.findDuplicateApiCalls = (timeWindow) => debugHelper.findDuplicateCalls(timeWindow);
}

export default debugHelper;
