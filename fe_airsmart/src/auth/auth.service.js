import { auth } from "../../src/firebase"
import { signInWithEmailAndPassword } from "firebase/auth";
import { errorMessagesFirebaseAuth } from "../utils/errorMessagesFirebaseAuth";

export const signin = async (email, password) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } catch (error) {
      throw new Error(errorMessagesFirebaseAuth[error.code] || error.message);
    }
  }