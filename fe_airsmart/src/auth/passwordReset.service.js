import api from '../services/apiService';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000';

/**
 * Send password reset email for regular users
 * @param {string} email - User email
 * @returns {Promise} API response
 */
export const sendPasswordResetEmail = async (email) => {
  try {
    const response = await fetch(`${API_BASE_URL}auth/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to send password reset email');
    }

    return data;
  } catch (error) {
    console.error('Password reset email error:', error);
    throw error;
  }
};

/**
 * Verify reset token validity
 * @param {string} token - Reset token
 * @returns {Promise} Validation result
 */
export const verifyResetToken = async (token) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/verify-reset-token?token=${token}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to verify reset token');
    }

    return data;
  } catch (error) {
    console.error('Token verification error:', error);
    throw error;
  }
};

/**
 * Reset password with token
 * @param {string} token - Reset token
 * @param {string} newPassword - New password
 * @returns {Promise} API response
 */
export const resetPassword = async (token, newPassword) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token, newPassword }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to reset password');
    }

    return data;
  } catch (error) {
    console.error('Password reset error:', error);
    throw error;
  }
};