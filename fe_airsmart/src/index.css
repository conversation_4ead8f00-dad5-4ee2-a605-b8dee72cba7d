body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Ensure hotspots always stay below dialogs */
.hotspot-marker {
  z-index: 100 !important;
  position: relative !important;
}

/* Ensure all dialogs stay above hotspots */
.MuiDialog-root {
  z-index: 1400 !important;
}

.MuiDrawer-root {
  z-index: 1400 !important;
}
