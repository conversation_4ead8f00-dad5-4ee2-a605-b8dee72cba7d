# Firebase Configuration
# Get these from Firebase Console > Project Settings > Service Accounts
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_PRIVATE_KEY="-----B<PERSON><PERSON> PRIVATE KEY-----\nyour_actual_private_key_here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your-project.iam.gserviceaccount.com

# Wasabi S3-Compatible Storage Configuration
# Sign up at https://wasabi.com and get your credentials
WASABI_ACCESS_KEY=your_actual_wasabi_access_key
WASABI_SECRET_KEY=your_actual_wasabi_secret_key
WASABI_BUCKET_NAME=airsmart
WASABI_REGION=ap-southeast-2
WASABI_ENDPOINT=https://s3.ap-southeast-2.wasabisys.com

# Application Configuration
PORT=4000
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# File Upload Configuration
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=.obj,.gltf,.glb,.fbx,.dae,.3ds
