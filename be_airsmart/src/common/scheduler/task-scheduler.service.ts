import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { AuthService } from '../../modules/auth/auth.service';

@Injectable()
export class TaskSchedulerService {
  private readonly logger = new Logger(TaskSchedulerService.name);

  constructor(private readonly authService: AuthService) {}

  // Chạy mỗi 10 phút để dọn dẹp token hết hạn
//   @Cron('0 */1 * * * *') // Cron pattern: giây phút giờ ngày tháng năm
//   async handleExpiredTokenCleanup(): Promise<void> {
//     this.logger.log(
//       '🕐 Starting automatic cleanup of expired password reset tokens...',
//     );

//     try {
//       const startTime = Date.now();

//       // Gọi hàm cleanup từ AuthService
//       await this.authService.cleanupExpiredTokens();

//       const endTime = Date.now();
//       const duration = endTime - startTime;

//       this.logger.log(
//         `✅ Password reset token cleanup completed in ${duration}ms`,
//       );
//     } catch (error) {
//       this.logger.error('❌ Error during password reset token cleanup:', error);
//     }
//   }

  // Chạy mỗi ngày lúc 2:00 AM để dọ
  // n dẹp token cũ (backup cleanup)
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async handleDailyTokenCleanup(): Promise<void> {
    this.logger.log(
      '🌙 Starting daily cleanup of old password reset tokens...',
    );

    try {
      const startTime = Date.now();

      // Dọn dẹp các token cũ hơn 24 giờ (kể cả chưa hết hạn)
      await this.authService.cleanupExpiredTokens();

      const endTime = Date.now();
      const duration = endTime - startTime;

      this.logger.log(`✅ Daily token cleanup completed in ${duration}ms`);
    } catch (error) {
      this.logger.error('❌ Error during daily token cleanup:', error);
    }
  }

  // Chạy mỗi giờ để log thống kê token
//   @Cron(CronExpression.EVERY_HOUR)
//   async logTokenStatistics(): Promise<void> {
//     try {
//       const stats = await this.authService.getTokenStatistics();
//       this.logger.log(
//         `📊 Password Reset Token Stats - Active: ${stats.activeTokens}, Expired: ${stats.expiredTokens}`,
//       );
//     } catch (error) {
//       this.logger.warn(
//         '⚠️ Could not retrieve token statistics:',
//         (error as Error).message,
//       );
//     }
//   }
}
