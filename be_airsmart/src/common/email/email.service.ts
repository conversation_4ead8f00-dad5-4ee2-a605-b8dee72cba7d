/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    // Configure SMTP transporter
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER, // your SMTP username
        pass: process.env.SMTP_PASS, // your SMTP password (App Password for Gmail)
      },
      tls: {
        rejectUnauthorized: false,
      },
    });
  }

  async sendPasswordResetEmail(
    email: string,
    resetToken: string,
    userName?: string,
  ): Promise<void> {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`;

    const mailOptions = {
      from: `"AirSmart Support" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
      to: email,
      subject: 'Reset Your AirSmart Password',
      html: this.getPasswordResetEmailTemplate(
        userName || 'User',
        resetUrl,
        resetToken,
      ),
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log(`✅ Password reset email sent to: ${email}`);
    } catch (error) {
      console.error('❌ Error sending password reset email:', error);
      throw new Error('Failed to send password reset email');
    }
  }

  // async sendPasswordResetEmailAdmin(
  //   email: string,
  //   resetToken: string,
  //   userName?: string,
  // ): Promise<void> {
  //   const resetUrl = `${process.env.ADMIN_FRONTEND_URL || 'http://localhost:3001'}/reset-password?token=${resetToken}`;

  //   const mailOptions = {
  //     from: `"AirSmart Admin Support" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
  //     to: email,
  //     subject: 'Reset Your AirSmart Admin Password',
  //     html: this.getPasswordResetEmailTemplateAdmin(
  //       userName || 'Admin',
  //       resetUrl,
  //       resetToken,
  //     ),
  //   };

  //   try {
  //     await this.transporter.sendMail(mailOptions);
  //     console.log(`✅ Admin password reset email sent to: ${email}`);
  //   } catch (error) {
  //     console.error('❌ Error sending admin password reset email:', error);
  //     throw new Error('Failed to send password reset email');
  //   }
  // }

  private getPasswordResetEmailTemplate(
    userName: string,
    resetUrl: string,
    token: string,
  ): string {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your AirSmart Password</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: white; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color:rgb(96, 104, 112); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background-color:rgb(87, 97, 106); color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>AirSmart Password Reset</h1>
      </div>
      <div class="content">
        <h2>Hello ${userName},</h2>
        <p>We received a request to reset your AirSmart account password. If you made this request, click the button below to reset your password:</p>
        
        <div style="text-align: center;">
          <a href="${resetUrl}" class="button">Reset My Password</a>
        </div>
        
        <p>Alternatively, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background-color: #e3f2fd; padding: 10px; border-radius: 5px;">${resetUrl}</p>
        
        <div class="warning">
          <strong>⚠️ Security Notice:</strong>
          <ul>
            <li>This link will expire in 1 hour for security reasons</li>
            <li>If you didn't request this password reset, please ignore this email</li>
            <li>Never share this link with anyone</li>
          </ul>
        </div>
        
        <p>Reset Token: <code>${token}</code></p>
        
        <p>If you continue to have problems, please contact our support team.</p>
        
        <p>Best regards,<br>The AirSmart Team</p>
      </div>
      <div class="footer">
        <p>© 2025 AirSmart. All rights reserved.</p>
        <p>This is an automated message, please do not reply to this email.</p>
      </div>
    </body>
    </html>
    `;
  }

  // private getPasswordResetEmailTemplateAdmin(
  //   userName: string,
  //   resetUrl: string,
  //   token: string,
  // ): string {
  //   return `
  //   <!DOCTYPE html>
  //   <html lang="en">
  //   <head>
  //     <meta charset="UTF-8">
  //     <meta name="viewport" content="width=device-width, initial-scale=1.0">
  //     <title>Reset Your AirSmart Admin Password</title>
  //     <style>
  //       body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
  //       .header { background-color: #d32f2f; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
  //       .content { background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
  //       .button { display: inline-block; background-color: #d32f2f; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
  //       .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
  //       .warning { background-color: #ffebee; border: 1px solid #ef9a9a; padding: 15px; margin: 20px 0; border-radius: 5px; }
  //     </style>
  //   </head>
  //   <body>
  //     <div class="header">
  //       <h1>🛡️ AirSmart Admin Password Reset</h1>
  //     </div>
  //     <div class="content">
  //       <h2>Hello ${userName},</h2>
  //       <p>We received a request to reset your AirSmart <strong>Admin Panel</strong> password. If you made this request, click the button below to reset your password:</p>
 
  //       <div style="text-align: center;">
  //         <a href="${resetUrl}" class="button">Reset Admin Password</a>
  //       </div>
   
  //       <p>Alternatively, you can copy and paste this link into your browser:</p>
  //       <p style="word-break: break-all; background-color: #ffebee; padding: 10px; border-radius: 5px;">${resetUrl}</p>
        
  //       <div class="warning">
  //         <strong>🔒 High Security Notice:</strong>
  //         <ul>
  //           <li>This is for <strong>ADMIN ACCESS</strong> - handle with extreme care</li>
  //           <li>This link will expire in 30 minutes for security reasons</li>
  //           <li>If you didn't request this password reset, contact security immediately</li>
  //           <li>Never share this link with anyone</li>
  //           <li>Use a strong password with at least 12 characters</li>
  //         </ul>
  //       </div>
        
  //       <p>Reset Token: <code>${token}</code></p>
        
  //       <p>If you continue to have problems or suspect unauthorized access, please contact our security team immediately.</p>
        
  //       <p>Best regards,<br>The AirSmart Security Team</p>
  //     </div>
  //     <div class="footer">
  //       <p>© 2024 AirSmart Admin Panel. All rights reserved.</p>
  //       <p>This is a high-priority security email. Do not reply to this automated message.</p>
  //     </div>
  //   </body>
  //   </html>
  //   `;
  // }

  async testEmailConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('✅ SMTP connection is ready');
      return true;
    } catch (error) {
      console.error('❌ SMTP connection failed:', error);
      return false;
    }
  }
}
