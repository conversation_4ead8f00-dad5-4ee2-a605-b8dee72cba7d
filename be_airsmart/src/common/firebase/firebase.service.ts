/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-require-imports */
import { Injectable, OnModuleInit } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { Firestore } from 'firebase-admin/firestore';
import { join } from 'path';

// Đường dẫn tuyệt đối đến file serviceAccountKey.json trong thư mục src
const serviceAccountPath = join(
  process.cwd(),
  'src/common/firebase/serviceAccountKey.json',
);
const serviceAccount = require(serviceAccountPath);

@Injectable()
export class FirebaseService implements OnModuleInit {
  private firestore: Firestore;
  private firebaseAdmin: typeof admin;

  onModuleInit() {
    try {
      // Kiểm tra xem Firebase đã được khởi tạo chưa
      if (!admin.apps.length) {
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
        });
      }
      this.firestore = admin.firestore();
      this.firebaseAdmin = admin;
    } catch (error) {
      console.error('Error initializing Firebase:', error);
      throw error;
    }
  }

  getDb() {
    return this.firestore;
  }

  getAdmin() {
    return this.firebaseAdmin;
  }

  getModulesCollection() {
    return this.firestore.collection('modules');
  }

  async getModuleById(moduleId: string) {
    const docRef = this.firestore.collection('modules').doc(moduleId);
    const docSnap = await docRef.get();
    if (!docSnap.exists) {
      return null;
    }
    return { id: docSnap.id, ...docSnap.data() };
  }

  async getStepsByModuleId(moduleId: string) {
    const stepsRef = this.firestore
      .collection('modules')
      .doc(moduleId)
      .collection('steps');
    const snapshot = await stepsRef.orderBy('order', 'asc').get();
    const steps = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    return steps;
  }

  async getQuizQuestionsByModuleId(moduleId: string) {
    const questionsRef = this.firestore
      .collection('modules')
      .doc(moduleId)
      .collection('quiz');
    const snapshot = await questionsRef.orderBy('order', 'asc').get();
    const questions = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
    return questions;
  }

  async getUserProgressByModuleId(userId: string, moduleId: string) {
    const progressRef = this.firestore
      .collection('userProgress')
      .doc(userId)
      .collection('modules')
      .doc(moduleId);
    const snapshot = await progressRef.get();
    if (!snapshot.exists) {
      return null;
    }
    return snapshot.data();
  }

  async updateUserProgress(
    userId: string,
    moduleId: string,
    data: { currentStepId?: string; completed?: boolean; quizScore?: number },
  ) {
    const progressRef = this.firestore
      .collection('userProgress')
      .doc(userId)
      .collection('modules')
      .doc(moduleId);

    const updateData = {
      ...data,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
    };

    await progressRef.set(updateData, { merge: true });
    return { success: true, userId, moduleId, ...updateData };
  }
}
