import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ModuleModule } from './modules/module/module.module';
import { QuizModule } from './modules/quiz/quiz.module';
import { UserProgressModule } from './modules/user-progress/user-progress.module';
import { FilesModule } from './modules/files/files.module';
import { UsersModule } from './modules/users/users.module';
import { AdminModule } from './modules/admin/admin-module.module';
import { AuthModule } from './modules/auth/auth.module';
import { SchedulerModule } from './common/scheduler/scheduler.module';
import { FirebaseAuthMiddleware } from './common/middleware/firebase-auth.middleware';
import { FirebaseService } from './common/firebase/firebase.service';
import { ZendeskModule } from './modules/zendesk_api/zendesk.module';
import { VoiceFlowModule } from './modules/voice_flow/voice_flow.module';
import { AdjustProgressModule } from './modules/adjust-progress/adjust-progress.module';
import { VoiceflowApiDataModule } from './modules/voiceflow-api-data/voiceflow-api-data.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    ModuleModule,
    QuizModule,
    UserProgressModule,
    FilesModule,
    UsersModule,
    AdminModule,
    AuthModule,
    SchedulerModule,
    ZendeskModule,
    VoiceFlowModule,
    AdjustProgressModule,
    VoiceflowApiDataModule,
  ],
  controllers: [AppController],
  providers: [AppService, FirebaseService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(FirebaseAuthMiddleware).forRoutes('*'); // Apply to all routes
  }
}
