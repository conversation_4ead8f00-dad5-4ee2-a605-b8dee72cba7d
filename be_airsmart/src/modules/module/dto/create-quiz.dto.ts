import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';

// Quiz Question DTO - Simplified to match database structure
export class CreateQuizQuestionDto {
  @IsOptional()
  @IsNumber()
  @Min(1)
  order?: number; // Will be auto-calculated if not provided

  @IsString()
  question: string;

  @IsArray()
  @IsString({ each: true })
  options: string[]; // Array of strings to match database structure

  @IsNumber()
  @Min(0)
  @Max(3)
  correctAnswer: number; // Index of correct option

  @IsOptional()
  @IsString()
  explanation?: string;
}