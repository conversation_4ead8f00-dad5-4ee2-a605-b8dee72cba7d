import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>A<PERSON>y,
  IsBoolean,
  <PERSON><PERSON>rl,
  <PERSON><PERSON>ength,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';

export class CreateModuleDto {
  @IsString()
  @MinLength(1, { message: 'Module name is required' })
  @MaxLength(100, { message: 'Module name must be less than 100 characters' })
  name: string;

  @IsString()
  @MinLength(1, { message: 'Module description is required' })
  @MaxLength(1000, {
    message: 'Module description must be less than 1000 characters',
  })
  description: string;

  @IsOptional()
  @IsString()
  course?: string; // Course ID - null for standalone modules

  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'Course order must be at least 1' })
  courseOrder?: number; // Order within course

  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'Order must be at least 1' })
  order?: number; // Global order for standalone modules

  @IsOptional()
  @IsUrl({}, { message: 'Model URL must be a valid URL' })
  modelUrl?: string;

  @IsOptional()
  @IsString()
  modelType?: string; // e.g., 'obj', 'gltf', etc.

  @IsOptional()
  @IsUrl({}, { message: 'Thumbnail must be a valid URL' })
  thumbnail?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsBoolean()
  isLocked?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Passing score must be at least 0' })
  @Max(100, { message: 'Passing score must be at most 100' })
  passingScore?: number;

  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Total steps must be at least 0' })
  totalSteps?: number;

  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Total quiz questions must be at least 0' })
  totalQuizQuestions?: number;

  @IsOptional()
  @IsBoolean()
  has3DModel?: boolean; // Flag to indicate if module has 3D model
}
