import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ModuleService } from './module.service';
import { ModuleController } from './module.controller';
// import { AdminModuleController } from './admin-module.controller';
import { FirebaseService } from 'src/common/firebase/firebase.service';
import { WasabiService } from 'src/common/wasabi/wasabi.service';

@Module({
  imports: [ConfigModule],
  controllers: [ModuleController],
  providers: [ModuleService, FirebaseService, WasabiService],
})
export class ModuleModule {}
