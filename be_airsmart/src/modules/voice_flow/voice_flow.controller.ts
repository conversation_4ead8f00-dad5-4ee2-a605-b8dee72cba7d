/* eslint-disable @typescript-eslint/no-unsafe-return */
import { <PERSON>, Post, Body, Get } from '@nestjs/common';
import { VoiceFlowService } from './voice_flow.service';
import * as crypto from 'crypto';

@Controller('voice-flow')
export class VoiceFlowController {
  constructor(private readonly voiceFlowService: VoiceFlowService) {}

  @Post('interact')
  async interact(
    @Body('sessionId') sessionId: string,
    @Body('message') message: string,
  ) {
    return await this.voiceFlowService.interact(sessionId, message);
  }

  @Get('session')
  async getSession() {
    // Tạo sessionId ngẫu nhiên (UUID)
    const sessionId = crypto.randomUUID();
    return { sessionId };
  }
}
