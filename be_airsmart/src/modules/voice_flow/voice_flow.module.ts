import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { VoiceFlowController } from './voice_flow.controller';
import { VoiceFlowService } from './voice_flow.service';

@Module({
  imports: [ConfigModule, HttpModule],
  controllers: [VoiceFlowController],
  providers: [VoiceFlowService],
})
export class VoiceFlowModule {}
