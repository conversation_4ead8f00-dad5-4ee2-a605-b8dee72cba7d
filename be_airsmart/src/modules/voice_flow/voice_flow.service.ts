/* eslint-disable @typescript-eslint/no-unsafe-return */
import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosError } from 'axios';

@Injectable()
export class VoiceFlowService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async interact(sessionId: string, message: string) {
    const apiKey = this.configService.get<string>('VOICEFLOW_API_KEY');
    const versionID =
      this.configService.get<string>('VOICEFLOW_VERSION_ID') || 'production';
    if (!apiKey) throw new Error('VOICEFLOW_API_KEY not set in .env');

    const isDMKey = apiKey.startsWith('VF.DM.');
    const url = isDMKey
      ? `https://general-runtime.voiceflow.com/state/${versionID}/user/${sessionId}/interact`
      : `https://general-runtime.voiceflow.com/state/${sessionId}/interact`;

    const headers = isDMKey
      ? {
          Authorization: apiKey,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        }
      : {
          Authorization: apiKey,
          versionID,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        };

    try {
      const response = await lastValueFrom(
        this.httpService.post(
          url,
          { request: { type: 'text', payload: message } },
          { headers },
        ),
      );
      return response.data;
    } catch (e) {
      const err = e as AxiosError;
      console.error('VoiceFlow API error:', {
        message: err.message,
        code: err.code,
        status: err.response?.status,
        data: err.response?.data,
        url,
        headers,
      });
      throw new Error(
        `Voiceflow error: ${err.message} - ${JSON.stringify(err.response?.data || {})}`,
      );
    }
  }
}
