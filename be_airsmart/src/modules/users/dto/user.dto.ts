export interface CreateUserDto {
  email: string;
  password: string;
  displayName: string;
  role: 'installer' | 'engineer' | 'architect' | 'salesperson';
}

export interface UpdateUserDto {
  displayName?: string;
  email?: string;
  role?: 'installer' | 'engineer' | 'architect' | 'salesperson';
}

export interface UserQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
