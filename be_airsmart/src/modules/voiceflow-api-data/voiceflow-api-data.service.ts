/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Injectable } from '@nestjs/common';
import { FirebaseService } from 'src/common/firebase/firebase.service';

@Injectable()
export class VoiceflowApiDataService {
  constructor(private readonly firebaseService: FirebaseService) {}

  /**
   * Get all courses with modules and steps for Voiceflow
   * @param userInfo - User information (optional)
   * @returns Complete data structure: courses → modules → steps
   */
  async getCoursesWithModulesAndSteps(userInfo?: {
    uid: string;
    lockedCourses?: string[];
  }) {
    try {
      const db = this.firebaseService.getDb();

      // Get all courses
      const coursesSnapshot = await db
        .collection('courses')
        .orderBy('order', 'asc')
        .get();

      const result: any[] = [];

      for (const courseDoc of coursesSnapshot.docs) {
        const courseData = courseDoc.data();

        // Skip inactive courses
        if (courseData.isActive === false) {
          continue;
        }

        // Skip locked courses for this user
        if (userInfo?.lockedCourses?.includes(courseDoc.id)) {
          continue;
        }

        // Get modules for this course
        const modulesSnapshot = await db
          .collection('modules')
          .where('course', '==', courseDoc.id)
          .get();

        const modulesWithSteps: any[] = [];

        for (const moduleDoc of modulesSnapshot.docs) {
          const moduleData = moduleDoc.data();

          // Skip locked modules
          if (moduleData.isLocked) {
            continue;
          }

          // Get steps (topics) for this module
          const stepsSnapshot = await db
            .collection('modules')
            .doc(moduleDoc.id)
            .collection('steps')
            .orderBy('order', 'asc')
            .get();

          const steps = stepsSnapshot.docs.map((stepDoc) => ({
            id: stepDoc.id,
            ...stepDoc.data(),
            // Clean up timestamps for better JSON serialization
            createdAt:
              stepDoc.data().createdAt?.toDate?.() || stepDoc.data().createdAt,
            updatedAt:
              stepDoc.data().updatedAt?.toDate?.() || stepDoc.data().updatedAt,
          }));

          // Add module with steps
          modulesWithSteps.push({
            id: moduleDoc.id,
            ...moduleData,
            steps, // Include all steps/topics
            actualStepsCount: steps.length,
            // Clean up timestamps
            createdAt:
              moduleData.createdAt?.toDate?.() || moduleData.createdAt,
            updatedAt:
              moduleData.updatedAt?.toDate?.() || moduleData.updatedAt,
          });
        }

        // Sort modules by courseOrder in memory
        modulesWithSteps.sort((a: any, b: any) => {
          const orderA = a.courseOrder || 0;
          const orderB = b.courseOrder || 0;
          return orderA - orderB;
        });

        // Create course with modules and their steps
        const courseWithModulesAndSteps = {
          id: courseDoc.id,
          ...courseData,
          modules: modulesWithSteps,
          totalModules: modulesWithSteps.length,
          totalSteps: modulesWithSteps.reduce(
            (sum: number, module: any) => sum + (module.actualStepsCount || 0),
            0,
          ),
          totalQuizQuestions: modulesWithSteps.reduce(
            (sum: number, module: any) =>
              sum + (module.totalQuizQuestions || 0),
            0,
          ),
          type: 'container',
          isContainer: true,
          // Clean up timestamps
          createdAt: courseData.createdAt?.toDate?.() || courseData.createdAt,
          updatedAt: courseData.updatedAt?.toDate?.() || courseData.updatedAt,
        };

        result.push(courseWithModulesAndSteps);
      }

      // Get standalone modules (modules without course)
      const standaloneModulesSnapshot = await db
        .collection('modules')
        .where('course', '==', null)
        .get();

      const standaloneModulesWithSteps: any[] = [];

      for (const moduleDoc of standaloneModulesSnapshot.docs) {
        const moduleData = moduleDoc.data();

        // Skip locked modules
        if (moduleData.isLocked) {
          continue;
        }

        // Get steps for standalone module
        const stepsSnapshot = await db
          .collection('modules')
          .doc(moduleDoc.id)
          .collection('steps')
          .orderBy('order', 'asc')
          .get();

        const steps = stepsSnapshot.docs.map((stepDoc) => ({
          id: stepDoc.id,
          ...stepDoc.data(),
          createdAt:
            stepDoc.data().createdAt?.toDate?.() || stepDoc.data().createdAt,
          updatedAt:
            stepDoc.data().updatedAt?.toDate?.() || stepDoc.data().updatedAt,
        }));

        standaloneModulesWithSteps.push({
          id: moduleDoc.id,
          ...moduleData,
          steps,
          actualStepsCount: steps.length,
          type: 'module',
          createdAt:
            moduleData.createdAt?.toDate?.() || moduleData.createdAt,
          updatedAt:
            moduleData.updatedAt?.toDate?.() || moduleData.updatedAt,
        });
      }

      // Sort standalone modules by order in memory
      standaloneModulesWithSteps.sort((a: any, b: any) => {
        const orderA = a.order || 0;
        const orderB = b.order || 0;
        return orderA - orderB;
      });

      // Add standalone modules to result
      result.push(...standaloneModulesWithSteps);

      return result;
    } catch (error) {
      console.error('Error getting courses with modules and steps for Voiceflow:', error);
      throw error;
    }
  }

  /**
   * Get specific course with all modules and steps
   * @param courseId - Course ID
   * @returns Course with modules and steps
   */
  async getCourseWithModulesAndSteps(courseId: string) {
    try {
      const db = this.firebaseService.getDb();

      // Get course
      const courseDoc = await db.collection('courses').doc(courseId).get();

      if (!courseDoc.exists) {
        throw new Error('Course not found');
      }

      const courseData = courseDoc.data();

      // Get modules for this course
      const modulesSnapshot = await db
        .collection('modules')
        .where('course', '==', courseId)
        .get();

      const modulesWithSteps: any[] = [];

      for (const moduleDoc of modulesSnapshot.docs) {
        const moduleData = moduleDoc.data();

        // Get steps for this module
        const stepsSnapshot = await db
          .collection('modules')
          .doc(moduleDoc.id)
          .collection('steps')
          .orderBy('order', 'asc')
          .get();

        const steps = stepsSnapshot.docs.map((stepDoc) => ({
          id: stepDoc.id,
          ...stepDoc.data(),
          createdAt:
            stepDoc.data().createdAt?.toDate?.() || stepDoc.data().createdAt,
          updatedAt:
            stepDoc.data().updatedAt?.toDate?.() || stepDoc.data().updatedAt,
        }));

        modulesWithSteps.push({
          id: moduleDoc.id,
          ...moduleData,
          steps,
          actualStepsCount: steps.length,
          createdAt: moduleData.createdAt?.toDate?.() || moduleData.createdAt,
          updatedAt: moduleData.updatedAt?.toDate?.() || moduleData.updatedAt,
        });
      }

      // Sort modules by courseOrder
      modulesWithSteps.sort((a: any, b: any) => {
        const orderA = a.courseOrder || 0;
        const orderB = b.courseOrder || 0;
        return orderA - orderB;
      });

      return {
        id: courseDoc.id,
        ...courseData,
        modules: modulesWithSteps,
        totalModules: modulesWithSteps.length,
        totalSteps: modulesWithSteps.reduce(
          (sum: number, module: any) => sum + (module.actualStepsCount || 0),
          0,
        ),
        totalQuizQuestions: modulesWithSteps.reduce(
          (sum: number, module: any) => sum + (module.totalQuizQuestions || 0),
          0,
        ),
        createdAt: courseData?.createdAt?.toDate?.() || courseData?.createdAt,
        updatedAt: courseData?.updatedAt?.toDate?.() || courseData?.updatedAt,
      };
    } catch (error) {
      console.error('Error getting course with modules and steps:', error);
      throw error;
    }
  }

  /**
   * Get specific module with all steps
   * @param moduleId - Module ID
   * @returns Module with steps
   */
  async getModuleWithSteps(moduleId: string) {
    try {
      const db = this.firebaseService.getDb();

      // Get module
      const moduleDoc = await db.collection('modules').doc(moduleId).get();

      if (!moduleDoc.exists) {
        throw new Error('Module not found');
      }

      const moduleData = moduleDoc.data();

      // Get steps for this module
      const stepsSnapshot = await db
        .collection('modules')
        .doc(moduleId)
        .collection('steps')
        .orderBy('order', 'asc')
        .get();

      const steps = stepsSnapshot.docs.map((stepDoc) => ({
        id: stepDoc.id,
        ...stepDoc.data(),
        createdAt: stepDoc.data().createdAt?.toDate?.() || stepDoc.data().createdAt,
        updatedAt: stepDoc.data().updatedAt?.toDate?.() || stepDoc.data().updatedAt,
      }));

      return {
        id: moduleDoc.id,
        ...moduleData,
        steps,
        actualStepsCount: steps.length,
        createdAt: moduleData?.createdAt?.toDate?.() || moduleData?.createdAt,
        updatedAt: moduleData?.updatedAt?.toDate?.() || moduleData?.updatedAt,
      };
    } catch (error) {
      console.error('Error getting module with steps:', error);
      throw error;
    }
  }

  /**
   * Get specific step details
   * @param moduleId - Module ID
   * @param stepId - Step ID
   * @returns Step details
   */
  async getStepDetails(moduleId: string, stepId: string) {
    try {
      const db = this.firebaseService.getDb();

      // Get step
      const stepDoc = await db
        .collection('modules')
        .doc(moduleId)
        .collection('steps')
        .doc(stepId)
        .get();

      if (!stepDoc.exists) {
        throw new Error('Step not found');
      }

      const stepData = stepDoc.data();

      return {
        id: stepDoc.id,
        ...stepData,
        moduleId,
        createdAt:
          stepData?.createdAt?.toDate?.() || stepData?.createdAt,
        updatedAt:
          stepData?.updatedAt?.toDate?.() || stepData?.updatedAt,
      };
    } catch (error) {
      console.error('Error getting step details:', error);
      throw error;
    }
  }
}
