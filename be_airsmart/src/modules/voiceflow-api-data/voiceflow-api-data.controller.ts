import { <PERSON>, Get, Param, Req, Res } from '@nestjs/common';
import { Request, Response } from 'express';
import { VoiceflowApiDataService } from './voiceflow-api-data.service';

@Controller('voiceflow')
export class VoiceflowApiDataController {
  constructor(
    private readonly voiceflowApiDataService: VoiceflowApiDataService,
  ) {}

  /**
   * Get all courses with modules and steps for Voiceflow
   * API: GET /voiceflow/get-data
   */
  @Get('get-data')
  async getCoursesWithModulesAndSteps(@Req() req: Request) {
    // Extract user info from request (added by Firebase auth middleware)
    const userInfo = req.user
      ? {
          uid: req.user.uid,
          lockedCourses: req.user.lockedCourses || [],
        }
      : undefined;

    return this.voiceflowApiDataService.getCoursesWithModulesAndSteps(userInfo);
  }

  /**
   * Get specific course with all modules and steps
   * API: GET /voiceflow/course/:courseId
   */
//   @Get('course/:courseId')
//   async getCourseWithModulesAndSteps(@Param('courseId') courseId: string) {
//     return this.voiceflowApiDataService.getCourseWithModulesAndSteps(courseId);
//   }

//   /**
//    * Get specific module with all steps
//    * API: GET /voiceflow/module/:moduleId
//    */
//   @Get('module/:moduleId')
//   async getModuleWithSteps(@Param('moduleId') moduleId: string) {
//     return this.voiceflowApiDataService.getModuleWithSteps(moduleId);
//   }

//   /**
//    * Get specific step details
//    * API: GET /voiceflow/module/:moduleId/step/:stepId
//    */
//   @Get('module/:moduleId/step/:stepId')
//   async getStepDetails(
//     @Param('moduleId') moduleId: string,
//     @Param('stepId') stepId: string,
//   ) {
//     return this.voiceflowApiDataService.getStepDetails(moduleId, stepId);
//   }

  /**
   * SSR endpoint: renders data as HTML for Voiceflow Website KB crawling
   * API: GET /voiceflow/ssr-data
   */
  @Get('ssr-data')
  async getSSRData(@Req() req: Request, @Res() res: Response) {
    const userInfo = req.user
      ? { uid: req.user.uid, lockedCourses: req.user.lockedCourses || [] }
      : undefined;
    const data = await this.voiceflowApiDataService.getCoursesWithModulesAndSteps(userInfo);
    const html = `<!DOCTYPE html>
<html lang="en">
<head><meta charset="utf-8"><title>Voiceflow Data</title></head>
<body><pre>${JSON.stringify(data, null, 2)}</pre></body>
</html>`;
    res.header('Content-Type', 'text/html; charset=utf-8').send(html);
  }
}
