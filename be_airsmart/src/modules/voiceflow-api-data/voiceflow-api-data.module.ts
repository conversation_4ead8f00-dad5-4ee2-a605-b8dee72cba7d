import { Module } from '@nestjs/common';
import { VoiceflowApiDataController } from './voiceflow-api-data.controller';
import { VoiceflowApiDataService } from './voiceflow-api-data.service';
import { FirebaseService } from 'src/common/firebase/firebase.service';

@Module({
  controllers: [VoiceflowApiDataController],
  providers: [VoiceflowApiDataService, FirebaseService],
})
export class VoiceflowApiDataModule {}
