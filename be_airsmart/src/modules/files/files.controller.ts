/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  Body,
  BadRequestException,
  NotFoundException,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FilesService } from './files.service';

@Controller('files')
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  /**
   * Get files by user role
   * @param role - User role (installer, engineer, architect, salesperson)
   * @returns List of files for the role
   */
  @Get('role/:role')
  async getFilesByRole(@Param('role') role: string) {
    const validRoles = ['installer', 'engineer', 'architect', 'salesperson'];

    if (!validRoles.includes(role.toLowerCase())) {
      throw new BadRequestException(
        `Invalid role. Must be one of: ${validRoles.join(', ')}`,
      );
    }

    return this.filesService.getFilesByRole(role);
  }

  /**
   * Get file metadata and signed URL
   * @param role - User role
   * @param fileName - File name within the role folder
   * @returns File metadata with signed URL
   */
  @Get('role/:role/file/:fileName')
  async getFile(
    @Param('role') role: string,
    @Param('fileName') fileName: string,
  ) {
    const validRoles = ['installer', 'engineer', 'architect', 'salesperson'];
    if (!validRoles.includes(role.toLowerCase())) {
      throw new BadRequestException(
        `Invalid role. Must be one of: ${validRoles.join(', ')}`,
      );
    }

    const fileKey = `${role.toLowerCase()}/${fileName}`;
    try {
      return await this.filesService.getFileMetadata(fileKey);
    } catch {
      throw new NotFoundException(`File not found: ${fileName}`);
    }
  }

  /**
   * Generate signed URL for direct file access
   * @param role - User role
   * @param fileName - File name
   * @param expiresIn - URL expiration time in seconds
   * @param action - Action type: 'preview' or 'download'
   * @returns Signed URL with appropriate headers
   */
  @Get('role/:role/file/:fileName/url')
  getFileUrl(
    @Param('role') role: string,
    @Param('fileName') fileName: string,
    @Query('expires') expiresIn?: string, // FIXED: Accept as string first
    @Query('action') action?: string,
  ) {
    const validRoles = ['installer', 'engineer', 'architect', 'salesperson'];
    if (!validRoles.includes(role.toLowerCase())) {
      throw new BadRequestException(
        `Invalid role. Must be one of: ${validRoles.join(', ')}`,
      );
    }

    // FIXED: Parse expires to number and provide default
    const expires = expiresIn ? parseInt(expiresIn, 10) || 3600 : 3600;

    // FIXED: Decode fileName properly
    const decodedFileName = decodeURIComponent(fileName);
    const fileKey = `${role.toLowerCase()}/${decodedFileName}`;

    // NEW: Pass action to service for proper Content-Disposition handling
    const actionType = action === 'download' ? 'download' : 'preview';
    const url = this.filesService.generateSignedUrl(
      fileKey,
      expires,
      actionType,
    );

    if (!url) {
      throw new NotFoundException(`File not found: ${decodedFileName}`);
    }

    return {
      url,
      action: actionType,
      expiresIn: expires,
      expiresAt: new Date(Date.now() + expires * 1000),
    };
  }

  /**
   * Upload file to specific role folder
   * @param role - Target role folder
   * @param file - Uploaded file
   * @param metadata - File metadata (course, description)
   * @returns Upload result
   */
  @Post('role/:role/upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Param('role') role: string,
    @UploadedFile() file: Express.Multer.File,
    @Body()
    metadata: { course?: string; description?: string; targetRole?: string },
  ) {
    const validRoles = ['installer', 'engineer', 'architect', 'salesperson'];

    // Admin can specify target role, otherwise use the role from URL
    const targetRole = metadata.targetRole || role;

    if (!validRoles.includes(targetRole.toLowerCase())) {
      throw new BadRequestException(
        `Invalid target role. Must be one of: ${validRoles.join(', ')}`,
      );
    }

    if (!file) {
      throw new BadRequestException('No file provided');
    }

    return this.filesService.uploadFile(
      file,
      targetRole.toLowerCase(),
      metadata,
    );
  }

  /**
   * Upload file to multiple role folders
   * @param file - Uploaded file
   * @param metadata - File metadata with target roles array
   * @returns Upload results for all roles
   */
  @Post('upload-to-multiple-roles')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFileToMultipleRoles(
    @UploadedFile() file: Express.Multer.File,
    @Body()
    metadata: {
      course?: string;
      description?: string;
      targetRoles: string | string[];
    },
  ) {
    const validRoles = ['installer', 'engineer', 'architect', 'salesperson'];

    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Parse targetRoles - handle both string and array
    let targetRoles: string[];
    if (typeof metadata.targetRoles === 'string') {
      try {
        targetRoles = JSON.parse(metadata.targetRoles);
      } catch {
        // If JSON.parse fails, treat as single role
        targetRoles = [metadata.targetRoles];
      }
    } else {
      targetRoles = metadata.targetRoles || [];
    }

    if (!targetRoles || targetRoles.length === 0) {
      throw new BadRequestException(
        'At least one target role must be specified',
      );
    }

    // Validate all target roles
    for (const role of targetRoles) {
      if (!validRoles.includes(role.toLowerCase())) {
        throw new BadRequestException(
          `Invalid role '${role}'. Must be one of: ${validRoles.join(', ')}`,
        );
      }
    }

    console.log('🔄 Uploading file to multiple roles:', targetRoles);

    return this.filesService.uploadFileToMultipleRoles(
      file,
      targetRoles.map((role) => role.toLowerCase()),
      {
        course: metadata.course,
        description: metadata.description,
      },
    );
  }

  /**
   * Delete file from role folder
   * @param role - Role folder
   * @param fileName - File name to delete
   * @returns Deletion result
   */
  @Delete('role/:role/file/:fileName')
  async deleteFile(
    @Param('role') role: string,
    @Param('fileName') fileName: string,
  ) {
    const validRoles = ['installer', 'engineer', 'architect', 'salesperson'];

    if (!validRoles.includes(role.toLowerCase())) {
      throw new BadRequestException(
        `Invalid role. Must be one of: ${validRoles.join(', ')}`,
      );
    }

    const fileKey = `${role.toLowerCase()}/${fileName}`;

    return this.filesService.deleteFile(fileKey);
  }
}
