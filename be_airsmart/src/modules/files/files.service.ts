/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';

@Injectable()
export class FilesService {
  private s3: AWS.S3;

  constructor() {
    // Configure AWS SDK for Wasabi
    this.s3 = new AWS.S3({
      accessKeyId: process.env.WASABI_ACCESS_KEY || 'OQL1BX7MOF71KL0MM0UM',
      secretAccessKey:
        process.env.WASABI_SECRET_KEY ||
        '07e80kBbTY2T9kPr1E43kMiq1PkO6lw9LUDhKeMi',
      endpoint: 'https://s3.ap-southeast-2.wasabisys.com',
      region: 'ap-southeast-2',
      s3ForcePathStyle: true,
    });
  }

  /**
   * Get files for a specific user role
   * @param userRole - User role (installer, engineer, architect, salesperson)
   * @returns List of files in the role folder
   */
  async getFilesByRole(userRole: string) {
    try {
      const bucket = 'airsmart';
      const prefix = `${userRole.toLowerCase()}/`;

      const params = {
        Bucket: bucket,
        Prefix: prefix,
        Delimiter: '/',
      };

      console.log('🔍 Listing files with params:', params);
      const result = await this.s3.listObjectsV2(params).promise();
      console.log('📁 S3 Response:', {
        IsTruncated: result.IsTruncated,
        KeyCount: result.KeyCount,
        Contents: result.Contents?.map((item) => ({
          Key: item.Key,
          Size: item.Size,
          LastModified: item.LastModified,
        })),
      });

      const files =
        result.Contents?.filter(
          (item) =>
            item.Key !== prefix && // Exclude the folder itself
            item.Key && // Ensure Key exists
            !item.Key.endsWith('/'), // Exclude folders
        ).map((item) => ({
          key: item.Key!,
          name: item.Key!.replace(prefix, ''),
          size: item.Size || 0,
          lastModified: item.LastModified,
          // NEW: Provide both preview and download URLs like fe_airsmart
          url: this.generateSignedUrl(item.Key!, 3600, 'preview'),
          previewUrl: this.generateSignedUrl(item.Key!, 3600, 'preview'),
          downloadUrl: this.generateSignedUrl(item.Key!, 3600, 'download'),
        })) || [];

      return {
        role: userRole,
        files,
        total: files.length,
      };
    } catch (error) {
      console.error('Error listing files:', error);
      throw new Error(`Failed to list files for role: ${userRole}`);
    }
  }

  /**
   * Generate signed URL for file access
   * @param key - File key in S3
   * @param expiresIn - URL expiration time in seconds (default: 1 hour)
   * @param action - Action type: 'preview' or 'download' (NEW parameter)
   * @returns Signed URL with appropriate Content-Disposition
   */
  generateSignedUrl(
    key: string,
    expiresIn: number = 3600,
    action: string = 'preview',
  ): string | null {
    try {
      const params: any = {
        Bucket: 'airsmart',
        Key: key,
        Expires: expiresIn,
      };

      // NEW: Add Content-Disposition based on action type
      if (action === 'download') {
        const fileName = key.split('/').pop() || 'download';
        params.ResponseContentDisposition = `attachment; filename="${fileName}"`;
      } else {
        // For preview, use inline disposition
        params.ResponseContentDisposition = 'inline';
      }

      return this.s3.getSignedUrl('getObject', params);
    } catch (error) {
      console.error('Error generating signed URL:', error);
      return null;
    }
  }

  /**
   * Get file metadata
   * @param key - File key in S3
   * @returns File metadata
   */
  async getFileMetadata(key: string) {
    try {
      const params = {
        Bucket: 'airsmart',
        Key: key,
      };

      const result = await this.s3.headObject(params).promise();

      return {
        key,
        name: key.split('/').pop(),
        size: result.ContentLength,
        contentType: result.ContentType,
        lastModified: result.LastModified,
        url: this.generateSignedUrl(key),
      };
    } catch (error) {
      console.error('Error getting file metadata:', error);
      throw new Error(`File not found: ${key}`);
    }
  }

  /**
   * Check if user has access to a specific file
   * @param userRole - User role
   * @param fileKey - File key
   * @returns Boolean indicating access permission
   */
  hasFileAccess(userRole: string, fileKey: string): boolean {
    const rolePrefix = `${userRole.toLowerCase()}/`;
    return fileKey.startsWith(rolePrefix);
  }

  /**
   * Upload file to Wasabi S3
   * @param file - Uploaded file
   * @param role - Target role folder
   * @param metadata - File metadata
   * @returns Upload result
   */
  async uploadFile(
    file: Express.Multer.File,
    role: string,
    metadata: { course?: string; description?: string } = {},
  ) {
    try {
      const fileKey = `${role}/${file.originalname}`;
      const uploadParams = {
        Bucket: 'airsmart',
        Key: fileKey,
        Body: file.buffer,
        ContentType: file.mimetype,
        Metadata: {
          course: metadata.course || 'general',
          description: metadata.description || '',
          uploadedAt: new Date().toISOString(),
          originalName: file.originalname,
          size: file.size.toString(),
        },
      };

      const result = await this.s3.upload(uploadParams).promise();

      return {
        success: true,
        fileKey,
        url: result.Location,
        signedUrl: this.generateSignedUrl(fileKey),
        metadata: {
          originalName: file.originalname,
          size: file.size,
          contentType: file.mimetype,
          course: metadata.course || 'general',
          description: metadata.description || '',
          uploadedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Upload file to multiple role folders
   * @param file - Uploaded file
   * @param roles - Array of target role folders
   * @param metadata - File metadata
   * @returns Upload results for all roles
   */
  async uploadFileToMultipleRoles(
    file: Express.Multer.File,
    roles: string[],
    metadata: { course?: string; description?: string } = {},
  ) {
    try {
      console.log('🔄 Uploading file to multiple roles:', roles);

      const uploadPromises = roles.map(async (role) => {
        const fileKey = `${role.toLowerCase()}/${file.originalname}`;
        const uploadParams = {
          Bucket: 'airsmart',
          Key: fileKey,
          Body: file.buffer,
          ContentType: file.mimetype,
          Metadata: {
            course: metadata.course || 'general',
            description: metadata.description || '',
            uploadedAt: new Date().toISOString(),
            originalName: file.originalname,
            size: file.size.toString(),
          },
        };

        const result = await this.s3.upload(uploadParams).promise();

        return {
          role,
          success: true,
          fileKey,
          url: result.Location,
          signedUrl: this.generateSignedUrl(fileKey),
        };
      });

      const results = await Promise.all(uploadPromises);

      console.log('✅ File uploaded to all roles successfully');

      return {
        success: true,
        totalRoles: roles.length,
        results,
        metadata: {
          originalName: file.originalname,
          size: file.size,
          contentType: file.mimetype,
          course: metadata.course || 'general',
          description: metadata.description || '',
          uploadedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error('Error uploading file to multiple roles:', error);
      throw new Error(
        `Failed to upload file to multiple roles: ${error.message}`,
      );
    }
  }

  /**
   * Delete file from Wasabi S3
   * @param fileKey - File key to delete
   * @returns Deletion result
   */
  async deleteFile(fileKey: string) {
    try {
      const deleteParams = {
        Bucket: 'airsmart',
        Key: fileKey,
      };

      await this.s3.deleteObject(deleteParams).promise();

      return {
        success: true,
        message: `File ${fileKey} deleted successfully`,
        deletedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }
}
