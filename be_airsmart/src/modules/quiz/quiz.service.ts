/* eslint-disable @typescript-eslint/no-unsafe-return */
import { Injectable } from '@nestjs/common';
import { FirebaseService } from 'src/common/firebase/firebase.service';

@Injectable()
export class QuizService {
  constructor(private readonly firebaseService: FirebaseService) {}

  /**
   * Get quiz questions for a specific module
   * @param moduleId - The ID of the module
   * @returns Array of quiz questions
   */
  async getQuizQuestions(moduleId: string) {
    try {
      const db = this.firebaseService.getDb();
      const questionsRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('quiz');

      // Create a query to get questions ordered by their order field
      const snapshot = await questionsRef.orderBy('order', 'asc').get();

      if (snapshot.empty) {
        return [];
      }

      // Map the documents to an array of question objects, loại bỏ correctAnswer
      const questions = snapshot.docs.map((doc) => {
        const data = doc.data();
        // Loại bỏ correctAnswer nếu có
        const question = { ...data };
        delete question.correctAnswer;
        return {
          id: doc.id,
          ...question,
        };
      });

      return questions;
    } catch (error) {
      console.error('Error getting quiz questions:', error);
      throw error;
    }
  }

  /**
   * Get a specific quiz question
   * @param moduleId - The ID of the module
   * @param questionId - The ID of the question
   * @returns The question object
   */
  async getQuizQuestion(moduleId: string, questionId: string) {
    try {
      const db = this.firebaseService.getDb();
      const questionRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('quiz')
        .doc(questionId);

      const snapshot = await questionRef.get();

      if (!snapshot.exists) {
        return null;
      }

      return {
        id: snapshot.id,
        ...snapshot.data(),
      };
    } catch (error) {
      console.error('Error getting quiz question:', error);
      throw error;
    }
  }

  /**
   * Get the passing score for a module's quiz
   * @param moduleId - The ID of the module
   * @returns The passing score (default: 70)
   */
  async getQuizPassingScore(moduleId: string) {
    try {
      const db = this.firebaseService.getDb();
      const moduleRef = db.collection('modules').doc(moduleId);

      const snapshot = await moduleRef.get();

      if (!snapshot.exists) {
        return 70; // Default passing score
      }

      const moduleData = snapshot.data();

      // Return the passing score or default to 70%
      return moduleData && moduleData.quizPassingScore
        ? moduleData.quizPassingScore
        : 70;
    } catch (error) {
      console.error('Error getting quiz passing score:', error);
      return 70; // Default passing score in case of error
    }
  }

  /**
   * Get correct answers and explanations for all quiz questions in a module
   * @param moduleId - The ID of the module
   * @returns Array of {id, correctAnswer, explanation}
   */
  async getQuizAnswers(moduleId: string) {
    try {
      const db = this.firebaseService.getDb();
      const questionsRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('quiz');
      const snapshot = await questionsRef.orderBy('order', 'asc').get();
      if (snapshot.empty) {
        return [];
      }
      return snapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          id: doc.id,
          correctAnswer: data.correctAnswer,
          explanation: data.explanation || '',
        };
      });
    } catch (error) {
      console.error('Error getting quiz answers:', error);
      throw error;
    }
  }
}
