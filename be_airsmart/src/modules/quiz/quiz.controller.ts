/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Controller, Get, Param } from '@nestjs/common';
import { QuizService } from './quiz.service';

@Controller('quiz')
export class QuizController {
  constructor(private readonly quizService: QuizService) {}

  @Get(':moduleId/questions')
  async getQuizQuestions(@Param('moduleId') moduleId: string) {
    return this.quizService.getQuizQuestions(moduleId);
  }

  @Get(':moduleId/questions/:questionId')
  async getQuizQuestion(
    @Param('moduleId') moduleId: string,
    @Param('questionId') questionId: string,
  ) {
    return this.quizService.getQuizQuestion(moduleId, questionId);
  }

  @Get(':moduleId/passing-score')
  async getQuizPassingScore(@Param('moduleId') moduleId: string) {
    return {
      passingScore: await this.quizService.getQuizPassingScore(moduleId),
    };
  }

  @Get(':moduleId/answers')
  async getQuizAnswers(@Param('moduleId') moduleId: string) {
    return this.quizService.getQuizAnswers(moduleId);
  }
}
