/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { FirebaseService } from '../../common/firebase/firebase.service';
import { EmailService } from '../../common/email/email.service';
import * as crypto from 'crypto';

interface PasswordResetToken {
  email: string;
  token: string;
  expiresAt: Date;
  isAdmin: boolean;
  createdAt: Date;
}

@Injectable()
export class AuthService {
  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly emailService: EmailService,
  ) {}

  async sendPasswordResetEmail(email: string): Promise<void> {
    try {
      console.log(`🔄 Processing password reset for: ${email}`);

      // Check if user exists in Firebase Auth
      const firebaseAdmin = this.firebaseService.getAdmin();

      let userRecord;
      try {
        userRecord = await firebaseAdmin.auth().getUserByEmail(email);
        console.log(`✅ User found in Firebase Auth: ${userRecord.uid}`);
      } catch {
        // Don't reveal if user exists or not for security
        console.log(
          `❌ Password reset requested for non-existent email: ${email}`,
        );
        return;
      }

      // Get user data from Firestore
      const firestore = this.firebaseService.getDb();
      const userDoc = await firestore
        .collection('users')
        .doc(userRecord.uid)
        .get();

      if (!userDoc.exists) {
        console.log(`❌ User document not found for: ${email}`);
        return;
      }

      const userData = userDoc.data();
      console.log(`📄 User data found: ${userData?.displayName || 'No name'}`);

      // Generate reset token
      const resetToken = this.generateResetToken();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1); // 1 hour expiry for regular users

      console.log(`🔑 Generated reset token: ${resetToken}`);
      console.log(`⏰ Token expires at: ${expiresAt.toISOString()}`);

      // Store reset token in Firestore
      const tokenData: PasswordResetToken = {
        email,
        token: resetToken,
        expiresAt,
        isAdmin: false,
        createdAt: new Date(),
      };

      console.log(`💾 Saving token to Firestore...`);
      await firestore
        .collection('passwordResetTokens')
        .doc(resetToken)
        .set(tokenData);

      console.log(`✅ Token saved to Firestore successfully`);

      // Send email
      console.log(`📧 Sending password reset email...`);
      await this.emailService.sendPasswordResetEmail(
        email,
        resetToken,
        userData?.displayName || userRecord.displayName,
      );

      console.log(`✅ Password reset email sent to: ${email}`);
    } catch (error) {
      console.error('❌ Error in sendPasswordResetEmail:', error);
      throw new BadRequestException('Failed to send password reset email');
    }
  }

  // async sendPasswordResetEmailAdmin(email: string): Promise<void> {
  //   try {
  //     console.log(`🔄 Processing admin password reset for: ${email}`);

  //     // Check if user exists in Firebase Auth
  //     const firebaseAdmin = this.firebaseService.getAdmin();

  //     let userRecord;
  //     try {
  //       userRecord = await firebaseAdmin.auth().getUserByEmail(email);
  //       console.log(`✅ Admin user found in Firebase Auth: ${userRecord.uid}`);
  //     } catch {
  //       // Don't reveal if user exists or not for security
  //       console.log(
  //         `❌ Admin password reset requested for non-existent email: ${email}`,
  //       );
  //       return;
  //     }

  //     // Check if user has admin privileges
  //     const idTokenResult = await firebaseAdmin.auth().getUser(userRecord.uid);
  //     const customClaims = idTokenResult.customClaims || {};

  //     if (!['admin', 'architect'].includes(customClaims.role as string)) {
  //       console.log(
  //         `❌ Non-admin user tried to reset admin password: ${email}`,
  //       );
  //       return; // Don't reveal if user is admin or not
  //     }

  //     console.log(`✅ User has admin privileges: ${customClaims.role}`);

  //     // Get user data from Firestore
  //     const firestore = this.firebaseService.getDb();
  //     const userDoc = await firestore
  //       .collection('users')
  //       .doc(userRecord.uid)
  //       .get();

  //     if (!userDoc.exists) {
  //       console.log(`❌ Admin user document not found for: ${email}`);
  //       return;
  //     }

  //     const userData = userDoc.data();
  //     console.log(
  //       `📄 Admin user data found: ${userData?.displayName || 'No name'}`,
  //     );

  //     // Generate reset token
  //     const resetToken = this.generateResetToken();
  //     const expiresAt = new Date();
  //     expiresAt.setMinutes(expiresAt.getMinutes() + 30); // 30 minutes expiry for admin users

  //     console.log(`🔑 Generated admin reset token: ${resetToken}`);
  //     console.log(`⏰ Admin token expires at: ${expiresAt.toISOString()}`);

  //     // Store reset token in Firestore
  //     const tokenData: PasswordResetToken = {
  //       email,
  //       token: resetToken,
  //       expiresAt,
  //       isAdmin: true,
  //       createdAt: new Date(),
  //     };

  //     console.log(`💾 Saving admin token to Firestore...`);
  //     await firestore
  //       .collection('passwordResetTokens')
  //       .doc(resetToken)
  //       .set(tokenData);

  //     console.log(`✅ Admin token saved to Firestore successfully`);

  //     // Send admin email
  //     console.log(`📧 Sending admin password reset email...`);
  //     await this.emailService.sendPasswordResetEmailAdmin(
  //       email,
  //       resetToken,
  //       userData?.displayName || userRecord.displayName,
  //     );

  //     console.log(`✅ Admin password reset email sent to: ${email}`);
  //   } catch (error) {
  //     console.error('❌ Error in sendPasswordResetEmailAdmin:', error);
  //     throw new BadRequestException(
  //       'Failed to send admin password reset email',
  //     );
  //   }
  // }

  async verifyResetToken(token: string): Promise<boolean> {
    try {
      console.log(`🔍 [VERIFY] Checking token: ${token}`);

      const firestore = this.firebaseService.getDb();
      const tokenDoc = await firestore
        .collection('passwordResetTokens')
        .doc(token)
        .get();

      console.log(`📄 [VERIFY] Token document exists: ${tokenDoc.exists}`);

      if (!tokenDoc.exists) {
        console.log(`❌ [VERIFY] Token not found in database: ${token}`);
        return false;
      }

      const tokenData = tokenDoc.data() as any;
      console.log(`📅 [VERIFY] Token found for email: ${tokenData.email}`);

      // Handle different timestamp formats
      const now = new Date();
      let expiresAt: Date;

      try {
        if (tokenData.expiresAt) {
          // Check if it's a Firestore Timestamp
          if (
            typeof tokenData.expiresAt === 'object' &&
            tokenData.expiresAt.toDate &&
            typeof tokenData.expiresAt.toDate === 'function'
          ) {
            expiresAt = tokenData.expiresAt.toDate();
          }
          // Check if it has seconds and nanoseconds (Firestore Timestamp structure)
          else if (
            typeof tokenData.expiresAt === 'object' &&
            tokenData.expiresAt._seconds !== undefined
          ) {
            expiresAt = new Date(tokenData.expiresAt._seconds * 1000);
          }
          // Try to parse as string or number
          else {
            expiresAt = new Date(tokenData.expiresAt);
          }
        } else {
          console.log(`❌ [VERIFY] No expiresAt field found`);
          return false;
        }
      } catch (parseError) {
        console.error(`❌ [VERIFY] Error parsing expiresAt:`, parseError);
        return false;
      }

      // Validate the parsed date
      if (!expiresAt || isNaN(expiresAt.getTime())) {
        console.log(`❌ [VERIFY] Invalid expiration date`);
        return false;
      }

      console.log(`⏰ [VERIFY] Current time: ${now.toISOString()}`);
      console.log(`⏰ [VERIFY] Token expires: ${expiresAt.toISOString()}`);

      const minutesRemaining =
        (expiresAt.getTime() - now.getTime()) / (1000 * 60);
      console.log(
        `⏰ [VERIFY] Minutes remaining: ${minutesRemaining.toFixed(2)}`,
      );

      const isExpired = now > expiresAt;
      console.log(`⏰ [VERIFY] Is expired: ${isExpired}`);

      if (isExpired) {
        console.log(
          `⏰ [VERIFY] Token has expired - but NOT deleting (only verify)`,
        );
        return false;
      }

      console.log(`✅ [VERIFY] Token is valid and not expired`);
      return true;
    } catch (error) {
      console.error('❌ [VERIFY] Error verifying reset token:', error);
      return false;
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      console.log(`🔄 [RESET] Starting password reset with token: ${token}`);

      const firestore = this.firebaseService.getDb();
      const tokenDoc = await firestore
        .collection('passwordResetTokens')
        .doc(token)
        .get();

      if (!tokenDoc.exists) {
        console.log(`❌ [RESET] Token not found: ${token}`);
        throw new BadRequestException('Invalid or expired reset token');
      }

      const tokenData = tokenDoc.data() as any;
      console.log(`📄 [RESET] Token found for email: ${tokenData.email}`);

      // Check if token is expired
      const now = new Date();
      let expiresAt: Date;

      try {
        if (tokenData.expiresAt) {
          // Handle Firestore Timestamp
          if (
            typeof tokenData.expiresAt === 'object' &&
            tokenData.expiresAt.toDate &&
            typeof tokenData.expiresAt.toDate === 'function'
          ) {
            expiresAt = tokenData.expiresAt.toDate();
          }
          // Handle Firestore Timestamp structure
          else if (
            typeof tokenData.expiresAt === 'object' &&
            tokenData.expiresAt._seconds !== undefined
          ) {
            expiresAt = new Date(tokenData.expiresAt._seconds * 1000);
          }
          // Parse as string or number
          else {
            expiresAt = new Date(tokenData.expiresAt);
          }
        } else {
          throw new BadRequestException('Invalid token format');
        }
      } catch (parseError) {
        console.error(`❌ [RESET] Error parsing expiresAt:`, parseError);
        throw new BadRequestException('Invalid token format');
      }

      console.log(`⏰ [RESET] Current time: ${now.toISOString()}`);
      console.log(`⏰ [RESET] Token expires: ${expiresAt.toISOString()}`);

      if (now > expiresAt) {
        console.log(`⏰ [RESET] Token has expired, deleting...`);
        // Clean up expired token
        await firestore.collection('passwordResetTokens').doc(token).delete();
        throw new BadRequestException('Reset token has expired');
      }

      // Get user by email
      const firebaseAdmin = this.firebaseService.getAdmin();
      let userRecord;

      try {
        userRecord = await firebaseAdmin.auth().getUserByEmail(tokenData.email);
        console.log(`👤 [RESET] User found: ${userRecord.uid}`);
      } catch (error) {
        console.log(`❌ [RESET] User not found: ${tokenData.email}`);
        // Still delete the token even if user not found
        await firestore.collection('passwordResetTokens').doc(token).delete();
        throw new NotFoundException('User not found');
      }

      // Update password in Firebase Auth
      console.log(`🔐 [RESET] Updating password for user: ${userRecord.uid}`);
      await firebaseAdmin.auth().updateUser(userRecord.uid, {
        password: newPassword,
      });

      // Update user document in Firestore
      console.log(`📝 [RESET] Updating user document...`);
      await firestore.collection('users').doc(userRecord.uid).update({
        updatedAt: new Date(),
        passwordLastChanged: new Date(),
      });

      // 🎯 CHỈ XÓA TOKEN KHI ĐÃ ĐỔI PASSWORD THÀNH CÔNG
      console.log(
        `🗑️ [RESET] Password updated successfully, now deleting token...`,
      );
      await firestore.collection('passwordResetTokens').doc(token).delete();

      console.log(
        `✅ [RESET] Password reset completed successfully for: ${tokenData.email}`,
      );
    } catch (error) {
      console.error('❌ [RESET] Error resetting password:', error);
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to reset password');
    }
  }

  // async testEmailConnection(): Promise<boolean> {
  //   try {
  //     return await this.emailService.testEmailConnection();
  //   } catch (error) {
  //     console.error('Email connection test failed:', error);
  //     return false;
  //   }
  // }

  private generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  // Cleanup expired tokens (should be run periodically)
  async cleanupExpiredTokens(): Promise<void> {
    try {
      const firestore = this.firebaseService.getDb();
      const now = new Date();

      const expiredTokens = await firestore
        .collection('passwordResetTokens')
        .where('expiresAt', '<', now)
        .get();

      const batch = firestore.batch();
      expiredTokens.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log(
        `🧹 Cleaned up ${expiredTokens.docs.length} expired reset tokens`,
      );
    } catch (error) {
      console.error('Error cleaning up expired tokens:', error);
    }
  }

  // Cleanup old tokens (older than 24 hours, even if not expired)
  async cleanupOldTokens(): Promise<void> {
    try {
      const firestore = this.firebaseService.getDb();
      const oneDayAgo = new Date();
      oneDayAgo.setHours(oneDayAgo.getHours() - 24);

      const oldTokens = await firestore
        .collection('passwordResetTokens')
        .where('createdAt', '<', oneDayAgo)
        .get();

      const batch = firestore.batch();
      oldTokens.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      console.log(
        `🧹 Cleaned up ${oldTokens.docs.length} old reset tokens (>24h)`,
      );
    } catch (error) {
      console.error('Error cleaning up old tokens:', error);
    }
  }

  // Get token statistics
  async getTokenStatistics(): Promise<{
    activeTokens: number;
    expiredTokens: number;
    totalTokens: number;
  }> {
    try {
      const firestore = this.firebaseService.getDb();
      const now = new Date();

      // Get all tokens
      const allTokensSnapshot = await firestore
        .collection('passwordResetTokens')
        .get();

      const totalTokens = allTokensSnapshot.size;
      let activeTokens = 0;
      let expiredTokens = 0;

      allTokensSnapshot.docs.forEach((doc) => {
        const tokenData = doc.data();
        let expiresAt: Date;

        try {
          // Handle different timestamp formats
          if (tokenData.expiresAt) {
            if (
              typeof tokenData.expiresAt === 'object' &&
              tokenData.expiresAt.toDate &&
              typeof tokenData.expiresAt.toDate === 'function'
            ) {
              expiresAt = tokenData.expiresAt.toDate();
            } else if (
              typeof tokenData.expiresAt === 'object' &&
              tokenData.expiresAt._seconds !== undefined
            ) {
              expiresAt = new Date(tokenData.expiresAt._seconds * 1000);
            } else {
              expiresAt = new Date(tokenData.expiresAt);
            }

            if (now > expiresAt) {
              expiredTokens++;
            } else {
              activeTokens++;
            }
          }
        } catch (error) {
          // If we can't parse the date, consider it expired
          expiredTokens++;
        }
      });

      return {
        activeTokens,
        expiredTokens,
        totalTokens,
      };
    } catch (error) {
      console.error('Error getting token statistics:', error);
      return {
        activeTokens: 0,
        expiredTokens: 0,
        totalTokens: 0,
      };
    }
  }
}
