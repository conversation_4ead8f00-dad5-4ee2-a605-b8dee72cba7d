import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Get,
  Query,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import {
  ForgotPasswordDto,
  ResetPasswordDto,
  VerifyResetTokenDto,
} from './dto/auth.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    await this.authService.sendPasswordResetEmail(forgotPasswordDto.email);
    return {
      success: true,
      message:
        'If an account with this email exists, a password reset link has been sent.',
    };
  }

  // @Post('forgot-password-admin')
  // @HttpCode(HttpStatus.OK)
  // async forgotPasswordAdmin(@Body() forgotPasswordDto: ForgotPasswordDto) {
  //   await this.authService.sendPasswordResetEmailAdmin(forgotPasswordDto.email);
  //   return {
  //     success: true,
  //     message:
  //       'If an admin account with this email exists, a password reset link has been sent.',
  //   };
  // }

  @Get('verify-reset-token')
  async verifyResetToken(@Query() verifyResetTokenDto: VerifyResetTokenDto) {
    const isValid = await this.authService.verifyResetToken(
      verifyResetTokenDto.token,
    );
    return {
      success: true,
      valid: isValid,
      message: isValid ? 'Token is valid' : 'Token is invalid or expired',
    };
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    await this.authService.resetPassword(
      resetPasswordDto.token,
      resetPasswordDto.newPassword,
    );
    return {
      success: true,
      message: 'Password has been reset successfully.',
    };
  }

  // @Get('test-email')
  // async testEmail() {
  //   const result = await this.authService.testEmailConnection();
  //   return {
  //     success: result,
  //     message: result
  //       ? 'Email connection is working'
  //       : 'Email connection failed',
  //   };
  // }

  @Get('token-stats')
  async getTokenStatistics() {
    const stats = await this.authService.getTokenStatistics();
    return {
      success: true,
      data: stats,
      message: 'Token statistics retrieved successfully',
    };
  }

  @Post('cleanup-expired-tokens')
  @HttpCode(HttpStatus.OK)
  async cleanupExpiredTokens() {
    await this.authService.cleanupExpiredTokens();
    return {
      success: true,
      message: 'Expired tokens cleanup completed',
    };
  }

  @Post('cleanup-old-tokens')
  @HttpCode(HttpStatus.OK)
  async cleanupOldTokens() {
    await this.authService.cleanupOldTokens();
    return {
      success: true,
      message: 'Old tokens cleanup completed',
    };
  }
}
