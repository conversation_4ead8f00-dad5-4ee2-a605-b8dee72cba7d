import { Module } from '@nestjs/common';
import { UserProgressService } from './user-progress.service';
import { UserProgressController } from './user-progress.controller';
import { FirebaseService } from 'src/common/firebase/firebase.service';
import { QuizService } from '../quiz/quiz.service';

@Module({
  controllers: [UserProgressController],
  providers: [UserProgressService, FirebaseService, QuizService],
})
export class UserProgressModule {}
