/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-return */
import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { UserProgressService } from './user-progress.service';

@Controller('user-progress')
export class UserProgressController {
  constructor(private readonly userProgressService: UserProgressService) {}

  @Get(':userId')
  async getUserProgress(@Param('userId') userId: string) {
    return this.userProgressService.getUserProgress(userId);
  }

  @Get(':userId/module/:moduleId')
  async getModuleProgress(
    @Param('userId') userId: string,
    @Param('moduleId') moduleId: string,
  ) {
    return this.userProgressService.getModuleProgress(userId, moduleId);
  }

  @Get(':userId/module/:moduleId/current-step')
  async getCurrentStepId(
    @Param('userId') userId: string,
    @Param('moduleId') moduleId: string,
  ) {
    const stepId = await this.userProgressService.getCurrentStepId(
      userId,
      moduleId,
    );
    return { stepId };
  }

  @Get(':userId/module/:moduleId/completed')
  async isModuleCompleted(
    @Param('userId') userId: string,
    @Param('moduleId') moduleId: string,
  ) {
    const completed = await this.userProgressService.isModuleCompleted(
      userId,
      moduleId,
    );
    return { completed };
  }

  @Post(':userId/module/:moduleId/update')
  async updateModuleProgress(
    @Param('userId') userId: string,
    @Param('moduleId') moduleId: string,
    @Body()
    updateData: {
      currentStepId?: string;
      completed?: boolean;
      quizScore?: number;
      quizPassed?: boolean;
    },
  ) {
    return this.userProgressService.updateModuleProgress(
      userId,
      moduleId,
      updateData.currentStepId || '',
      updateData.completed,
      updateData.quizScore,
      updateData.quizPassed,
    );
  }
}
