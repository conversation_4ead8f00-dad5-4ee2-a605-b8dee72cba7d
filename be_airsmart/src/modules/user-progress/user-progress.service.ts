/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-return */
import { Injectable } from '@nestjs/common';
import { FirebaseService } from 'src/common/firebase/firebase.service';
import { QuizService } from '../quiz/quiz.service';

@Injectable()
export class UserProgressService {
  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly quizService: QuizService,
  ) {}

  /**
   * Get user progress for all modules
   * @param userId - The ID of the user
   * @returns User progress data
   */
  async getUserProgress(userId: string) {
    try {
      const db = this.firebaseService.getDb();
      const progressRef = db
        .collection('userProgress')
        .doc(userId)
        .collection('moduleProgress');

      const snapshot = await progressRef.get();

      if (snapshot.empty) {
        return {};
      }

      const progress = {};
      snapshot.docs.forEach((doc) => {
        progress[doc.id] = doc.data();
      });

      return progress;
    } catch (error) {
      console.error('Error getting user progress:', error);
      throw error;
    }
  }

  /**
   * Get user progress for a specific module
   * @param userId - The ID of the user
   * @param moduleId - The ID of the module
   * @returns Module progress data
   */
  async getModuleProgress(userId: string, moduleId: string) {
    try {
      const db = this.firebaseService.getDb();
      const progressRef = db
        .collection('userProgress')
        .doc(userId)
        .collection('moduleProgress')
        .doc(moduleId);

      const snapshot = await progressRef.get();

      if (!snapshot.exists) {
        return null;
      }

      return snapshot.data();
    } catch (error) {
      console.error('Error getting module progress:', error);
      throw error;
    }
  }

  /**
   * Get the current step ID for a module
   * @param userId - The ID of the user
   * @param moduleId - The ID of the module
   * @returns Current step ID
   */
  async getCurrentStepId(userId: string, moduleId: string) {
    try {
      const progress = await this.getModuleProgress(userId, moduleId);

      if (!progress) {
        return null;
      }

      return progress.currentStepId || null;
    } catch (error) {
      console.error('Error getting current step ID:', error);
      throw error;
    }
  }

  /**
   * Check if a module is completed
   * @param userId - The ID of the user
   * @param moduleId - The ID of the module
   * @returns Whether the module is completed
   */
  async isModuleCompleted(userId: string, moduleId: string) {
    try {
      const progress = await this.getModuleProgress(userId, moduleId);

      if (!progress) {
        return false;
      }

      return progress.completed || false;
    } catch (error) {
      console.error('Error checking if module is completed:', error);
      throw error;
    }
  }

  /**
   * Update module progress
   * @param userId - The ID of the user
   * @param moduleId - The ID of the module
   * @param currentStepId - The ID of the current step
   * @param completed - Whether the module is completed (will be overridden based on quiz score)
   * @param quizScore - The quiz score (if applicable)
   * @param quizPassed - Whether the quiz is passed (if applicable)
   * @returns Updated progress data
   */
  async updateModuleProgress(
    userId: string,
    moduleId: string,
    currentStepId: string,
    completed: boolean = false,
    quizScore: number | null = null,
    quizPassed: boolean = false,
  ) {
    try {
      const db = this.firebaseService.getDb();
      const progressRef = db
        .collection('userProgress')
        .doc(userId)
        .collection('moduleProgress')
        .doc(moduleId);

      const updateData: any = {
        lastUpdated: new Date(),
      };

      // Handle quiz score logic
      if (quizScore !== null) {
        updateData.quizScore = quizScore;

        // Get the passing score for this module
        const passingScore =
          await this.quizService.getQuizPassingScore(moduleId);
        console.log(
          `Module ${moduleId} passing score: ${passingScore}, user score: ${quizScore}`,
        );

        if (quizScore >= passingScore) {
          // Quiz passed - complete the module
          updateData.quizPassed = true;
          updateData.completed = true;
          updateData.currentStepId = 'completed';
          console.log(`User passed quiz for module ${moduleId}`);
        } else {
          // Quiz failed - reset to first step
          updateData.quizPassed = false;
          updateData.completed = false;

          // Get the first step of the module to reset to
          const firstStep = await this.getFirstStepOfModule(moduleId);
          updateData.currentStepId = firstStep || 'step1';
          console.log(
            `User failed quiz for module ${moduleId}, reset to step: ${updateData.currentStepId}`,
          );
        }
      } else {
        // No quiz score provided - regular step progress update
        if (currentStepId) {
          updateData.currentStepId = currentStepId;
        }

        if (completed !== undefined) {
          updateData.completed = completed;
        }

        // Allow explicit setting of quizPassed
        if (quizPassed !== undefined) {
          updateData.quizPassed = quizPassed;
        }
      }

      await progressRef.set(updateData, { merge: true });

      return {
        success: true,
        userId,
        moduleId,
        ...updateData,
      };
    } catch (error) {
      console.error('Error updating module progress:', error);
      throw error;
    }
  }

  /**
   * Get the first step of a module
   * @param moduleId - The ID of the module
   * @returns The ID of the first step
   */
  private async getFirstStepOfModule(moduleId: string): Promise<string | null> {
    try {
      const db = this.firebaseService.getDb();
      const stepsRef = db
        .collection('modules')
        .doc(moduleId)
        .collection('steps');

      const snapshot = await stepsRef.orderBy('order', 'asc').limit(1).get();

      if (snapshot.empty) {
        return null;
      }

      return snapshot.docs[0].id;
    } catch (error) {
      console.error('Error getting first step of module:', error);
      return null;
    }
  }
}
