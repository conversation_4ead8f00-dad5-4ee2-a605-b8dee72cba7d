import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AdminModuleController } from './admin-module.controller';
import { ModuleService } from '../module/module.service';
import { FirebaseService } from 'src/common/firebase/firebase.service';
import { WasabiService } from 'src/common/wasabi/wasabi.service';

@Module({
  imports: [ConfigModule],
  controllers: [AdminModuleController],
  providers: [ModuleService, FirebaseService, WasabiService],
})
export class AdminModule {}
