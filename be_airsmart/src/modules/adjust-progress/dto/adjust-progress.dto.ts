import { IsString, IsOptional, IsBoolean, IsN<PERSON>ber } from 'class-validator';

export class AdjustUserProgressDto {
  @IsString()
  userId: string;

  @IsString()
  moduleId: string;

  @IsString()
  @IsOptional()
  currentStepId?: string;

  @IsOptional()
  @IsBoolean()
  completed?: boolean;

  @IsOptional()
  @IsBoolean()
  quizPassed?: boolean;

  @IsOptional()
  @IsNumber()
  quizScore?: number;

  @IsOptional()
  @IsBoolean()
  jumpToPoint?: boolean;
}

export class GetUserProgressDetailDto {
  @IsString()
  userId: string;
}

export class ResetUserProgressDto {
  @IsString()
  userId: string;

  @IsOptional()
  @IsString()
  moduleId?: string; // If provided, reset only this module, otherwise reset all
}
