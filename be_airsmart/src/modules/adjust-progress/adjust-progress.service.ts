/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-return */
import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { FirebaseService } from 'src/common/firebase/firebase.service';
import {
  AdjustUserProgressDto,
  ResetUserProgressDto,
} from './dto/adjust-progress.dto';

@Injectable()
export class AdjustProgressService {
  constructor(private readonly firebaseService: FirebaseService) {}

  /**
   * Get detailed user progress with course/module/step hierarchy
   * @param userId - The ID of the user
   * @returns Detailed progress data with hierarchy
   */
  async getUserProgressDetail(userId: string) {
    try {
      const db = this.firebaseService.getDb();

      // Get user progress
      const progressRef = db
        .collection('userProgress')
        .doc(userId)
        .collection('moduleProgress');

      const progressSnapshot = await progressRef.get();

      if (progressSnapshot.empty) {
        return {
          userId,
          courses: [],
          totalModules: 0,
          completedModules: 0,
          overallProgress: 0,
        };
      }

      // Get all modules data to build hierarchy
      const modulesRef = db.collection('modules');
      const modulesSnapshot = await modulesRef.get();

      const modules = {};
      // Load modules and their steps in parallel for better performance
      await Promise.all(
        modulesSnapshot.docs.map(async (doc) => {
          const moduleData: any = { id: doc.id, ...doc.data() };

          // Get steps for this module using the same pattern as admin
          try {
            const stepsSnapshot = await db
              .collection('modules')
              .doc(doc.id)
              .collection('steps')
              .orderBy('order', 'asc')
              .get();

            const steps = stepsSnapshot.docs.map((stepDoc) => ({
              id: stepDoc.id,
              ...stepDoc.data(),
            }));

            moduleData.steps = steps;
          } catch (error) {
            console.error(`Error loading steps for module ${doc.id}:`, error);
            moduleData.steps = [];
          }

          modules[doc.id] = moduleData;
        }),
      );

      // Get all courses data
      const coursesRef = db.collection('courses');
      const coursesSnapshot = await coursesRef.get();

      const courses = {};
      coursesSnapshot.docs.forEach((doc) => {
        courses[doc.id] = { id: doc.id, ...doc.data() };
      });

      // Build progress data
      const userProgress = {};
      progressSnapshot.docs.forEach((doc) => {
        userProgress[doc.id] = doc.data();
      });

      // Build hierarchy: Course -> Module -> Steps
      const result = this.buildProgressHierarchy(
        courses,
        modules,
        userProgress,
      );

      return {
        userId,
        ...result,
      };
    } catch (error) {
      console.error('Error getting user progress detail:', error);
      throw error;
    }
  }

  /**
   * Get all modules in order (standalone modules by order, course modules by courseOrder)
   * @returns Array of modules sorted by order
   */
  private async getAllModulesInOrder() {
    try {
      const db = this.firebaseService.getDb();

      // Get all modules
      const modulesSnapshot = await db.collection('modules').get();
      const modules = modulesSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // Separate standalone modules and course modules
      const standaloneModules = modules.filter((module: any) => !module.course);
      const courseModules = modules.filter((module: any) => module.course);

      // Sort standalone modules by order
      standaloneModules.sort(
        (a: any, b: any) => (a.order || 0) - (b.order || 0),
      );

      // Group course modules by course and sort by courseOrder
      const courseModulesGrouped = courseModules.reduce(
        (acc: any, module: any) => {
          if (!acc[module.course]) {
            acc[module.course] = [];
          }
          acc[module.course].push(module);
          return acc;
        },
        {},
      );

      // Sort modules within each course by courseOrder
      Object.keys(courseModulesGrouped).forEach((courseId) => {
        courseModulesGrouped[courseId].sort(
          (a: any, b: any) => (a.courseOrder || 0) - (b.courseOrder || 0),
        );
      });

      // Get courses and sort by order
      const coursesSnapshot = await db
        .collection('courses')
        .orderBy('order', 'asc')
        .get();
      const courses = coursesSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      // Build final ordered list: standalone modules first, then course modules in course order
      const orderedModules = [...standaloneModules];

      courses.forEach((course: any) => {
        if (courseModulesGrouped[course.id]) {
          orderedModules.push(...courseModulesGrouped[course.id]);
        }
      });
      return orderedModules;
    } catch (error) {
      console.error('Error getting modules in order:', error);
      throw error;
    }
  }

  /**
   * Adjust progress for modules relative to target module (auto-complete before, reset after)
   * @param userId - User ID
   * @param targetModuleId - Target module ID to jump to
   * @param action - 'complete' to auto-complete, 'reset' to reset progress
   * @returns Array of affected module IDs
   */
  private async adjustRelativeModules(
    userId: string,
    targetModuleId: string,
    action: 'complete' | 'reset',
  ) {
    try {
      const db = this.firebaseService.getDb();

      // Get all modules in order
      const orderedModules = await this.getAllModulesInOrder();

      // Find the index of target module
      const targetIndex = orderedModules.findIndex(
        (module: any) => module.id === targetModuleId,
      );

      if (targetIndex === -1) {
        throw new Error(`Target module ${targetModuleId} not found`);
      }

      // Get modules to process based on action
      const modulesToProcess =
        action === 'complete'
          ? orderedModules.slice(0, targetIndex) // Before target
          : orderedModules.slice(targetIndex + 1); // After target

      const affectedModules: string[] = [];
      const batch = db.batch();

      for (const module of modulesToProcess) {
        const progressRef = db
          .collection('userProgress')
          .doc(userId)
          .collection('moduleProgress')
          .doc(module.id);

        if (action === 'complete') {
          // Auto-complete module
          const existingProgress = await progressRef.get();
          const isAlreadyCompleted =
            existingProgress.exists && existingProgress.data()?.completed;

          if (!isAlreadyCompleted) {
            batch.set(
              progressRef,
              {
                currentStepId: 'completed',
                completed: true,
                lastUpdated: new Date(),
                autoCompleted: true,
              },
              { merge: true },
            );
            affectedModules.push(module.id);
          }
        } else {
          // Reset module progress
          batch.delete(progressRef);
          affectedModules.push(module.id);
        }
      }

      // Commit changes
      if (affectedModules.length > 0) {
        await batch.commit();
        console.log(
          `${action === 'complete' ? 'Auto-completed' : 'Reset'} ` +
            `${affectedModules.length} modules for user ${userId}`,
        );
      }

      return affectedModules;
    } catch (error) {
      console.error(
        `Error ${action === 'complete' ? 'auto-completing' : 'resetting'} modules:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Adjust user progress for a specific module or jump to a point
   * @param adjustDto - Adjustment data
   * @returns Updated progress
   */
  async adjustUserProgress(adjustDto: AdjustUserProgressDto) {
    try {
      const {
        userId,
        moduleId,
        currentStepId,
        completed,
        quizPassed,
        quizScore,
        jumpToPoint,
      } = adjustDto;

      const db = this.firebaseService.getDb();

      // Verify module exists
      const moduleRef = db.collection('modules').doc(moduleId);
      const moduleDoc = await moduleRef.get();

      if (!moduleDoc.exists) {
        throw new NotFoundException(`Module with ID ${moduleId} not found`);
      }

      // Verify step exists in module by loading steps from subcollection
      let stepExists = false;
      if (currentStepId && currentStepId !== 'completed') {
        try {
          const stepsRef = db
            .collection('modules')
            .doc(moduleId)
            .collection('steps');
          const stepsSnapshot = await stepsRef.get();
          stepExists = stepsSnapshot.docs.some(
            (stepDoc) => stepDoc.id === currentStepId,
          );

          if (!stepExists) {
            throw new BadRequestException(
              `Step ${currentStepId} not found in module ${moduleId}`,
            );
          }
        } catch (error) {
          console.error('Error verifying step:', error);
          throw new BadRequestException(
            `Error verifying step ${currentStepId} in module ${moduleId}`,
          );
        }
      }

      let autoCompletedModules: string[] = [];
      let resetModules: string[] = [];
      let newCompleted = completed;
      let newCurrentStepId = currentStepId;

      // Handle jump to point logic
      if (jumpToPoint) {
        // Auto-complete previous modules
        autoCompletedModules = await this.adjustRelativeModules(
          userId,
          moduleId,
          'complete',
        );

        // Reset subsequent modules
        resetModules = await this.adjustRelativeModules(
          userId,
          moduleId,
          'reset',
        );

        // Set target module to completed
        newCurrentStepId = 'completed';
        newCompleted = true;
      }
      // Auto-complete previous modules if completing normally
      else if (newCompleted === true || newCurrentStepId === 'completed') {
        try {
          autoCompletedModules = await this.adjustRelativeModules(
            userId,
            moduleId,
            'complete',
          );
          console.log(
            `Auto-completed ${autoCompletedModules.length} ` +
              `previous modules for user ${userId}`,
          );
        } catch (error) {
          console.error('Error auto-completing previous modules:', error);
        }
      }

      // Prepare update data (declare outside for return use)
      let updateData: any = null;

      // Update user progress for the target module
      const progressRef = db
        .collection('userProgress')
        .doc(userId)
        .collection('moduleProgress')
        .doc(moduleId);

      // If newCurrentStepId is null, we want to reset the module progress
      if (newCurrentStepId === null) {
        await progressRef.delete();
      } else {
        updateData = {
          currentStepId: newCurrentStepId,
          lastUpdated: new Date(),
          ...(newCompleted !== undefined && { completed: newCompleted }),
          ...(quizPassed !== undefined && { quizPassed }),
          ...(quizScore !== undefined && { quizScore }),
        };

        await progressRef.set(updateData, { merge: true });
      }

      return {
        success: true,
        message: `Progress adjusted for user ${userId} in module ${moduleId}`,
        data: updateData, // ✅ Now always defined (or null)
        autoCompletedModules:
          autoCompletedModules.length > 0 ? autoCompletedModules : undefined,
        resetModules: resetModules.length > 0 ? resetModules : undefined,
      };
    } catch (error) {
      console.error('Error adjusting user progress:', error);
      throw error;
    }
  }

  /**
   * Reset user progress (all modules or specific module)
   * @param resetDto - Reset data
   * @returns Reset result
   */
  async resetUserProgress(resetDto: ResetUserProgressDto) {
    try {
      const { userId, moduleId } = resetDto;
      const db = this.firebaseService.getDb();

      if (moduleId) {
        // Reset specific module
        const progressRef = db
          .collection('userProgress')
          .doc(userId)
          .collection('moduleProgress')
          .doc(moduleId);

        await progressRef.delete();

        return {
          success: true,
          message: `Progress reset for user ${userId} in module ${moduleId}`,
        };
      } else {
        // Reset all modules
        const progressRef = db
          .collection('userProgress')
          .doc(userId)
          .collection('moduleProgress');

        const snapshot = await progressRef.get();
        const batch = db.batch();

        snapshot.docs.forEach((doc) => {
          batch.delete(doc.ref);
        });

        await batch.commit();

        return {
          success: true,
          message: `All progress reset for user ${userId}`,
        };
      }
    } catch (error) {
      console.error('Error resetting user progress:', error);
      throw error;
    }
  }

  /**
   * Get all users with their current progress summary
   * @returns List of users with progress summary
   */
  async getAllUsersProgressSummary() {
    try {
      const db = this.firebaseService.getDb();

      // Get all users
      const usersRef = db.collection('users');
      const usersSnapshot = await usersRef.get();

      const usersSummary: any[] = [];

      for (const userDoc of usersSnapshot.docs) {
        const userData = userDoc.data();
        const userId = userDoc.id;

        // Get user progress
        const progressRef = db
          .collection('userProgress')
          .doc(userId)
          .collection('moduleProgress');

        const progressSnapshot = await progressRef.get();

        let totalModules = 0;
        let completedModules = 0;
        let currentModule: any = null;
        let lastActivity: Date | null = null;

        progressSnapshot.docs.forEach((doc) => {
          const progress = doc.data();
          totalModules++;

          if (progress.completed) {
            completedModules++;
          }

          // Find most recent activity
          if (
            progress.lastUpdated &&
            (!lastActivity || progress.lastUpdated.toDate() > lastActivity)
          ) {
            lastActivity = progress.lastUpdated.toDate();
            currentModule = {
              moduleId: doc.id,
              currentStepId: progress.currentStepId,
              completed: progress.completed || false,
            };
          }
        });

        usersSummary.push({
          userId,
          email: userData.email,
          displayName: userData.displayName || userData.email,
          totalModules,
          completedModules,
          progressPercentage:
            totalModules > 0
              ? Math.round((completedModules / totalModules) * 100)
              : 0,
          currentModule,
          lastActivity,
        });
      }

      return usersSummary.sort((a, b) => {
        if (!a.lastActivity && !b.lastActivity) return 0;
        if (!a.lastActivity) return 1;
        if (!b.lastActivity) return -1;
        return b.lastActivity.getTime() - a.lastActivity.getTime();
      });
    } catch (error) {
      console.error('Error getting users progress summary:', error);
      throw error;
    }
  }

  /**
   * Build progress hierarchy from courses, modules, and user progress
   */
  private buildProgressHierarchy(
    courses: any,
    modules: any,
    userProgress: any,
  ) {
    const coursesArray = Object.values(courses);
    const modulesArray = Object.values(modules);
    const result = {
      courses: [] as any[],
      totalModules: 0,
      completedModules: 0,
      overallProgress: 0,
    };

    // Sort courses by order
    coursesArray.sort((a: any, b: any) => (a.order || 0) - (b.order || 0));

    coursesArray.forEach((course: any) => {
      // Find modules that belong to this course
      const courseModules = modulesArray.filter(
        (module: any) => module.course === course.id,
      );

      // Sort course modules by courseOrder
      courseModules.sort(
        (a: any, b: any) => (a.courseOrder || 0) - (b.courseOrder || 0),
      );

      const courseProgress = {
        courseId: course.id,
        courseName: course.name,
        modules: [] as any[],
        totalModules: courseModules.length,
        completedModules: 0,
      };

      courseModules.forEach((module: any) => {
        const moduleId = module.id;
        const progress = userProgress[moduleId] || {};
        const steps = module.steps || [];

        // Sort steps by order
        steps.sort((a: any, b: any) => (a.order || 0) - (b.order || 0));

        const moduleProgress = {
          moduleId,
          moduleName: module.name,
          moduleOrder: module.courseOrder || 0,
          totalSteps: steps.length,
          currentStepId: progress.currentStepId || null,
          completed: progress.completed || false,
          quizPassed: progress.quizPassed || false,
          quizScore: progress.quizScore || 0,
          lastUpdated: progress.lastUpdated,
          autoCompleted: progress.autoCompleted || false,
          steps: steps.map((step: any) => ({
            stepId: step.id,
            title: step.title,
            order: step.order || 0,
            isCurrent: step.id === progress.currentStepId,
          })),
        };

        if (moduleProgress.completed) {
          courseProgress.completedModules++;
          result.completedModules++;
        }

        courseProgress.modules.push(moduleProgress);
        result.totalModules++;
      });

      // Only add course if it has modules
      if (courseModules.length > 0) {
        result.courses.push(courseProgress);
      }
    });

    // Add standalone modules (modules without course)
    const standaloneModules = modulesArray.filter(
      (module: any) => !module.course,
    );
    if (standaloneModules.length > 0) {
      // Sort standalone modules by order
      standaloneModules.sort(
        (a: any, b: any) => (a.order || 0) - (b.order || 0),
      );

      const standaloneProgress = {
        courseId: 'standalone',
        courseName: 'Standalone Modules',
        modules: [] as any[],
        totalModules: standaloneModules.length,
        completedModules: 0,
      };

      standaloneModules.forEach((module: any) => {
        const moduleId = module.id;
        const progress = userProgress[moduleId] || {};
        const steps = module.steps || [];

        // Sort steps by order
        steps.sort((a: any, b: any) => (a.order || 0) - (b.order || 0));

        const moduleProgress = {
          moduleId,
          moduleName: module.name,
          moduleOrder: module.order || 0,
          totalSteps: steps.length,
          currentStepId: progress.currentStepId || null,
          completed: progress.completed || false,
          quizPassed: progress.quizPassed || false,
          quizScore: progress.quizScore || 0,
          lastUpdated: progress.lastUpdated,
          autoCompleted: progress.autoCompleted || false,
          steps: steps.map((step: any) => ({
            stepId: step.id,
            title: step.title,
            order: step.order || 0,
            isCurrent: step.id === progress.currentStepId,
          })),
        };

        if (moduleProgress.completed) {
          standaloneProgress.completedModules++;
          result.completedModules++;
        }

        standaloneProgress.modules.push(moduleProgress);
        result.totalModules++;
      });

      // Add standalone modules at the beginning (before courses)
      result.courses.unshift(standaloneProgress);
    }

    result.overallProgress =
      result.totalModules > 0
        ? Math.round((result.completedModules / result.totalModules) * 100)
        : 0;

    return result;
  }
}
