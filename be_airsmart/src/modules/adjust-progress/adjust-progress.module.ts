import { Module } from '@nestjs/common';
import { AdjustProgressController } from './adjust-progress.controller';
import { AdjustProgressService } from './adjust-progress.service';
import { FirebaseService } from '../../common/firebase/firebase.service';

@Module({
  controllers: [AdjustProgressController],
  providers: [AdjustProgressService, FirebaseService],
  exports: [AdjustProgressService],
})
export class AdjustProgressModule {}