import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { AdjustProgressService } from './adjust-progress.service';
import {
  AdjustUserProgressDto,
  ResetUserProgressDto,
} from './dto/adjust-progress.dto';

@Controller('adjust-progress')
export class AdjustProgressController {
  constructor(private readonly adjustProgressService: AdjustProgressService) {}

  /**
   * Get all users with their progress summary
   * GET /adjust-progress/users-summary
   */
  @Get('users-summary')
  async getAllUsersProgressSummary() {
    try {
      const result = await this.adjustProgressService.getAllUsersProgressSummary();
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * Get detailed progress for a specific user
   * GET /adjust-progress/user/:userId/detail
   */
  @Get('user/:userId/detail')
  async getUserProgressDetail(@Param('userId') userId: string) {
    try {
      const result = await this.adjustProgressService.getUserProgressDetail(userId);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * Adjust user progress for a specific module
   * POST /adjust-progress/adjust
   */
  @Post('adjust')
  @HttpCode(HttpStatus.OK)
  async adjustUserProgress(@Body() adjustDto: AdjustUserProgressDto) {
    try {
      const result = await this.adjustProgressService.adjustUserProgress(adjustDto);
      return result;
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * Reset user progress (all modules or specific module)
   * DELETE /adjust-progress/reset
   */
  @Delete('reset')
  @HttpCode(HttpStatus.OK)
  async resetUserProgress(@Body() resetDto: ResetUserProgressDto) {
    try {
      const result = await this.adjustProgressService.resetUserProgress(resetDto);
      return result;
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  }
}