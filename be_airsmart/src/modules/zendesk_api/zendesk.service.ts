/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class ZendeskService {
  private readonly zendeskUrl =
    'https://airsmart1696.zendesk.com/api/v2/tickets.json';
  private readonly zendeskUploadUrl =
    'https://airsmart1696.zendesk.com/api/v2/uploads.json';
  private readonly zendeskEmail = process.env.ZENDESK_EMAIL;
  private readonly zendeskToken = process.env.ZENDESK_TOKEN;

  private getAuthHeader() {
    const auth = Buffer.from(
      `${this.zendeskEmail}/token:${this.zendeskToken}`,
    ).toString('base64');
    return `Basic ${auth}`;
  }

  async createTicket({
    name,
    email,
    subject,
    body,
    file,
  }: {
    name: string;
    email: string;
    subject: string;
    body: string;
    file?: Express.Multer.File;
  }) {
    let uploadToken: string | null = null;
    if (file) {
      // Upload file trước
      const formData = new (require('form-data'))();
      formData.append('file', file.buffer, { filename: file.originalname });
      formData.append('filename', file.originalname);
      try {
        const uploadRes = await axios.post(
          `${this.zendeskUploadUrl}?filename=${encodeURIComponent(file.originalname)}`,
          formData,
          {
            headers: {
              ...formData.getHeaders(),
              Authorization: this.getAuthHeader(),
            },
          },
        );
        uploadToken = uploadRes.data.upload.token;
      } catch (err) {
        throw new InternalServerErrorException(
          'Failed to upload file to Zendesk',
        );
      }
    }

    // Chuẩn bị payload
    const ticketPayload = {
      ticket: {
        subject,
        comment: {
          body,
          ...(uploadToken ? { uploads: [uploadToken] } : {}),
        },
        requester: {
          name,
          email,
        },
      },
    };

    try {
      const res = await axios.post(this.zendeskUrl, ticketPayload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: this.getAuthHeader(),
        },
      });
      return res.data;
    } catch (err) {
      throw new InternalServerErrorException(
        'Zendesk error: ' + (err.response?.data?.error || err.message),
      );
    }
  }
}
