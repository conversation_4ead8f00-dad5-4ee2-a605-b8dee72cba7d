/* eslint-disable @typescript-eslint/no-unsafe-return */
import {
  Controller,
  Post,
  Body,
  UseInterceptors,
  UploadedFile,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CreateZendeskTicketDto } from './dto/create-zendesk-ticket.dto';
import { ZendeskService } from './zendesk.service';

@Controller('zendesk')
export class ZendeskController {
  constructor(private readonly zendeskService: ZendeskService) {}

  @Post('ticket')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('file'))
  async createZendeskTicket(
    @Body() body: CreateZendeskTicketDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    const { name, email, subject, body: messageBody } = body;
    return this.zendeskService.createTicket({
      name,
      email,
      subject,
      body: messageBody,
      file,
    });
  }
}
