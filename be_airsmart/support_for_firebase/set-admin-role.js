const admin = require('firebase-admin');

// Initialize Firebase Admin with the service account
const serviceAccount = require('../src/common/firebase/serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// Get the user UID from command-line arguments
const uid = process.argv[2];

if (!uid) {
  console.error('❌ Error: User UID is required!');
  console.log('Usage: node set-admin-role.js USER_UID');
  process.exit(1);
}

async function setAdminRole(userId) {
  try {
    console.log(`🔍 Setting admin role for user: ${userId}`);

    // 1. Set custom claims in Firebase Auth
    await admin.auth().setCustomUserClaims(userId, { role: 'admin' });
    console.log('✅ Admin role set in Firebase Auth custom claims');

    // 2. Update role in Firestore (to keep things in sync)
    const db = admin.firestore();
    await db.collection('users').doc(userId).update({
      role: 'admin',
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log('✅ Admin role updated in Firestore database');

    console.log('🎉 Successfully set admin role for user!');
    console.log('ℹ️ Note: The user must sign out and sign back in for the changes to take effect.');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error setting admin role:', error);
    process.exit(1);
  }
}

// Run the function
setAdminRole(uid);